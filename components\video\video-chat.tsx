'use client';

import { useState } from 'react';
import { MessageCircle, FileText } from 'lucide-react';
import { VideoData, TranscriptData, ChatMessage } from '@/types/video.d';
import { useTranslations } from 'next-intl';
import VideoSummary from './video-summary';
import VideoChatContent from './video-chat-content';

interface VideoChatProps {
  messages: ChatMessage[];
  onSendMessage: (message: string) => void;
  transcript: TranscriptData | null;
  video: VideoData | null;
}

type TabType = 'chat' | 'summary';

export default function VideoChat({ messages, onSendMessage, transcript, video }: VideoChatProps) {
  const t = useTranslations('video');
  const [activeTab, setActiveTab] = useState<TabType>('chat');

  const tabs = [
    { id: 'chat', label: 'AI聊天', icon: MessageCircle, color: 'text-blue-600' },
    { id: 'summary', label: '视频总结', icon: FileText, color: 'text-green-600' },
  ] as const;

  return (
    <div className="h-full bg-white flex flex-col">
      {/* 标签页导航 */}
      <div className="border-b border-gray-200 bg-white flex-shrink-0">
        <div className="flex">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;

            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as TabType)}
                className={`flex items-center gap-2 px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                  isActive
                    ? 'border-blue-500 text-blue-600 bg-blue-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Icon className={`h-4 w-4 ${isActive ? tab.color : 'text-gray-400'}`} />
                {tab.label}
              </button>
            );
          })}
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'chat' ? (
          <VideoChatContent
            messages={messages}
            onSendMessage={onSendMessage}
            transcript={transcript}
            video={video}
          />
        ) : (
          <VideoSummary
            video={video}
            transcript={transcript}
          />
        )}
      </div>
    </div>
  );
}
