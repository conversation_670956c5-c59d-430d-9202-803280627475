'use client';

import { useState, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  FileVideo,
  Trash2,
  Clock
} from 'lucide-react';
import { VideoData } from '@/types/video';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

interface VideoSidebarProps {
  onVideoSelect: (video: VideoData) => void;
  selectedVideo: VideoData | null;
  isProcessing: boolean;
}

export default function VideoSidebar({ onVideoSelect, selectedVideo, isProcessing }: VideoSidebarProps) {
  const t = useTranslations('video');
  const [videos, setVideos] = useState<VideoData[]>([]);

  // Add video to library when selected from player
  const addVideoToLibrary = useCallback((video: VideoData) => {
    setVideos(prev => {
      const exists = prev.find(v => v.id === video.id);
      if (exists) return prev;
      return [video, ...prev];
    });
  }, []);

  // Update library when video is selected
  useEffect(() => {
    if (selectedVideo) {
      addVideoToLibrary(selectedVideo);
    }
  }, [selectedVideo, addVideoToLibrary]);

  const removeVideo = (videoId: string) => {
    setVideos(prev => prev.filter(v => v.id !== videoId));
    if (selectedVideo?.id === videoId) {
      onVideoSelect(videos[0] || null);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">{t('video_library')}</h2>
        <p className="text-sm text-gray-600 mt-1">{t('library_description')}</p>
      </div>

      {/* Video List */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-3">
            {t('recent_videos')} ({videos.length})
          </h3>
          
          {videos.length === 0 ? (
            <div className="text-center py-8">
              <FileVideo className="mx-auto h-12 w-12 text-gray-400 mb-3" />
              <p className="text-sm text-gray-500">{t('no_videos_yet')}</p>
              <p className="text-xs text-gray-400 mt-1">{t('upload_first_video')}</p>
            </div>
          ) : (
            <div className="space-y-2">
              {videos.map((video) => (
                <Card 
                  key={video.id}
                  className={`cursor-pointer transition-colors hover:bg-gray-50 ${
                    selectedVideo?.id === video.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                  }`}
                  onClick={() => onVideoSelect(video)}
                >
                  <CardContent className="p-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 truncate">
                          {video.name}
                        </h4>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {video.type === 'file' ? t('local_file') : t('url')}
                          </Badge>
                          {video.size && (
                            <span className="text-xs text-gray-500">
                              {formatFileSize(video.size)}
                            </span>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          {video.uploadedAt.toLocaleDateString()}
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeVideo(video.id);
                        }}
                        className="h-8 w-8 p-0"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Processing Status */}
      {isProcessing && (
        <div className="p-4 border-t border-gray-200 bg-blue-50">
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-sm text-blue-600">{t('processing_video')}</span>
          </div>
        </div>
      )}
    </div>
  );
}
