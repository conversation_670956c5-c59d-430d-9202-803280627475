'use client';

import { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Upload, 
  Link, 
  Video, 
  Clock, 
  FileVideo, 
  Trash2,
  Play,
  Pause,
  MoreVertical
} from 'lucide-react';
import { VideoData } from '@/types/video';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

interface VideoSidebarProps {
  onVideoSelect: (video: VideoData) => void;
  selectedVideo: VideoData | null;
  isProcessing: boolean;
}

export default function VideoSidebar({ onVideoSelect, selectedVideo, isProcessing }: VideoSidebarProps) {
  const t = useTranslations('video');
  const [videos, setVideos] = useState<VideoData[]>([]);
  const [urlInput, setUrlInput] = useState('');
  const [inputMethod, setInputMethod] = useState<'file' | 'url'>('file');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateVideoUrl = (url: string): boolean => {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname.toLowerCase();
      const validExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'];
      return validExtensions.some(ext => pathname.endsWith(ext)) ||
             url.includes('video') ||
             url.includes('youtube') ||
             url.includes('vimeo');
    } catch {
      return false;
    }
  };

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      // Check file type
      const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/webm'];
      if (!allowedTypes.includes(selectedFile.type)) {
        toast.error('Please select a valid video file (MP4, AVI, MOV, WMV, FLV, WebM)');
        return;
      }

      // Check file size (max 100MB)
      if (selectedFile.size > 100 * 1024 * 1024) {
        toast.error('File size must be less than 100MB');
        return;
      }

      const newVideo: VideoData = {
        id: Date.now().toString(),
        name: selectedFile.name,
        url: URL.createObjectURL(selectedFile),
        file: selectedFile,
        size: selectedFile.size,
        type: 'file',
        uploadedAt: new Date(),
      };

      setVideos(prev => [newVideo, ...prev]);
      onVideoSelect(newVideo);
      toast.success('Video uploaded successfully');
    }
  }, [onVideoSelect]);

  const handleUrlSubmit = () => {
    if (!urlInput || !validateVideoUrl(urlInput)) {
      toast.error('Please enter a valid video URL');
      return;
    }

    const newVideo: VideoData = {
      id: Date.now().toString(),
      name: urlInput.split('/').pop() || 'Video from URL',
      url: urlInput,
      type: 'url',
      uploadedAt: new Date(),
    };

    setVideos(prev => [newVideo, ...prev]);
    onVideoSelect(newVideo);
    setUrlInput('');
    toast.success('Video URL added successfully');
  };

  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const droppedFile = event.dataTransfer.files[0];
    if (droppedFile && droppedFile.type.startsWith('video/')) {
      const fakeEvent = {
        target: { files: [droppedFile] }
      } as React.ChangeEvent<HTMLInputElement>;
      handleFileSelect(fakeEvent);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  }, []);

  const removeVideo = (videoId: string) => {
    setVideos(prev => prev.filter(v => v.id !== videoId));
    if (selectedVideo?.id === videoId) {
      onVideoSelect(videos[0] || null);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">{t('sidebar_title')}</h2>
        <p className="text-sm text-gray-600 mt-1">{t('sidebar_description')}</p>
      </div>

      {/* Upload Section */}
      <div className="p-4 border-b border-gray-200">
        <Tabs value={inputMethod} onValueChange={(value) => setInputMethod(value as 'file' | 'url')}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="file" className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              {t('upload_file')}
            </TabsTrigger>
            <TabsTrigger value="url" className="flex items-center gap-2">
              <Link className="h-4 w-4" />
              {t('from_url')}
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="file" className="mt-4">
            <div
              className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-gray-400 transition-colors"
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onClick={() => fileInputRef.current?.click()}
            >
              <Video className="mx-auto h-8 w-8 text-gray-400 mb-2" />
              <p className="text-sm font-medium text-gray-900 mb-1">
                {t('drop_video_here')}
              </p>
              <p className="text-xs text-gray-500">
                {t('supported_formats')}
              </p>
              <input
                ref={fileInputRef}
                type="file"
                accept="video/*"
                onChange={handleFileSelect}
                className="hidden"
              />
            </div>
          </TabsContent>
          
          <TabsContent value="url" className="mt-4">
            <div className="space-y-3">
              <Input
                type="url"
                placeholder={t('video_url_placeholder')}
                value={urlInput}
                onChange={(e) => setUrlInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleUrlSubmit()}
              />
              <Button 
                onClick={handleUrlSubmit} 
                disabled={!urlInput}
                className="w-full"
              >
                <Link className="h-4 w-4 mr-2" />
                {t('add_video')}
              </Button>
              <p className="text-xs text-gray-500">
                {t('url_description')}
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Video List */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-3">
            {t('recent_videos')} ({videos.length})
          </h3>
          
          {videos.length === 0 ? (
            <div className="text-center py-8">
              <FileVideo className="mx-auto h-12 w-12 text-gray-400 mb-3" />
              <p className="text-sm text-gray-500">{t('no_videos_yet')}</p>
              <p className="text-xs text-gray-400 mt-1">{t('upload_first_video')}</p>
            </div>
          ) : (
            <div className="space-y-2">
              {videos.map((video) => (
                <Card 
                  key={video.id}
                  className={`cursor-pointer transition-colors hover:bg-gray-50 ${
                    selectedVideo?.id === video.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                  }`}
                  onClick={() => onVideoSelect(video)}
                >
                  <CardContent className="p-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 truncate">
                          {video.name}
                        </h4>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {video.type === 'file' ? t('local_file') : t('url')}
                          </Badge>
                          {video.size && (
                            <span className="text-xs text-gray-500">
                              {formatFileSize(video.size)}
                            </span>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          {video.uploadedAt.toLocaleDateString()}
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeVideo(video.id);
                        }}
                        className="h-8 w-8 p-0"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Processing Status */}
      {isProcessing && (
        <div className="p-4 border-t border-gray-200 bg-blue-50">
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-sm text-blue-600">{t('processing_video')}</span>
          </div>
        </div>
      )}
    </div>
  );
}
