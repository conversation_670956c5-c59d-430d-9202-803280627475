'use client';

import { useState, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  FileVideo,
  Trash2,
  Clock,
  Play
} from 'lucide-react';
import { VideoData } from '@/types/video';
import { useTranslations } from 'next-intl';

interface VideoSidebarProps {
  onVideoSelect: (video: VideoData) => void;
  selectedVideo: VideoData | null;
  isProcessing: boolean;
}

export default function VideoSidebar({ onVideoSelect, selectedVideo, isProcessing }: VideoSidebarProps) {
  const t = useTranslations('video');
  const [videos, setVideos] = useState<VideoData[]>([]);

  // Add video to library when selected from player
  const addVideoToLibrary = useCallback((video: VideoData) => {
    setVideos(prev => {
      const exists = prev.find(v => v.id === video.id);
      if (exists) return prev;
      return [video, ...prev];
    });
  }, []);

  // Update library when video is selected
  useEffect(() => {
    if (selectedVideo) {
      addVideoToLibrary(selectedVideo);
    }
  }, [selectedVideo, addVideoToLibrary]);

  const removeVideo = (videoId: string) => {
    setVideos(prev => prev.filter(v => v.id !== videoId));
    if (selectedVideo?.id === videoId) {
      onVideoSelect(videos[0] || null);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };



  return (
    <div className="h-full bg-white flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
        <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <FileVideo className="h-5 w-5 text-blue-600" />
          {t('video_library')}
        </h2>
        <p className="text-sm text-gray-600 mt-1">{t('library_description')}</p>
      </div>

      {/* Video List */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-3">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold text-gray-900">
              {t('recent_videos')}
            </h3>
            <Badge variant="secondary" className="text-xs">
              {videos.length}
            </Badge>
          </div>

          {videos.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FileVideo className="h-8 w-8 text-gray-400" />
              </div>
              <p className="text-sm text-gray-500 mb-1">{t('no_videos_yet')}</p>
              <p className="text-xs text-gray-400">{t('upload_first_video')}</p>
            </div>
          ) : (
            <div className="space-y-2">
              {videos.map((video) => (
                <Card
                  key={video.id}
                  className={`cursor-pointer transition-all duration-200 hover:shadow-md border ${
                    selectedVideo?.id === video.id
                      ? 'ring-2 ring-blue-500 bg-blue-50 border-blue-200'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => onVideoSelect(video)}
                >
                  <CardContent className="p-2.5">
                    {/* Video Thumbnail */}
                    <div className="relative mb-2 bg-gray-100 rounded-md overflow-hidden" style={{ aspectRatio: '16/9' }}>
                      <video
                        src={video.url}
                        className="w-full h-full object-cover"
                        preload="metadata"
                        muted
                        onError={(e) => {
                          // Hide video and show fallback if it fails to load
                          e.currentTarget.style.display = 'none';
                          const fallback = e.currentTarget.nextElementSibling as HTMLElement;
                          if (fallback && fallback.classList.contains('video-fallback')) {
                            fallback.style.display = 'flex';
                          }
                        }}
                      />
                      {/* Fallback for failed video loads */}
                      <div className="video-fallback absolute inset-0 bg-gray-200 hidden items-center justify-center">
                        <div className="text-center">
                          <FileVideo className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                          <p className="text-xs text-gray-500">Preview unavailable</p>
                        </div>
                      </div>
                      <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                        <div className="w-6 h-6 bg-white bg-opacity-80 rounded-full flex items-center justify-center">
                          <Play className="h-3 w-3 text-gray-700 ml-0.5" />
                        </div>
                      </div>
                      {/* Duration overlay */}
                      <div className="absolute bottom-1 right-1 bg-black bg-opacity-70 text-white text-xs px-1 py-0.5 rounded">
                        {video.duration ? formatTime(video.duration) : '--:--'}
                      </div>
                    </div>

                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 truncate mb-1.5">
                          {video.name}
                        </h4>
                        <div className="flex items-center gap-1.5 mb-1.5">
                          <Badge
                            variant="outline"
                            className={`text-xs px-1.5 py-0.5 ${
                              video.type === 'file'
                                ? 'bg-green-50 text-green-700 border-green-200'
                                : 'bg-blue-50 text-blue-700 border-blue-200'
                            }`}
                          >
                            {video.type === 'file' ? t('local_file') : t('url')}
                          </Badge>
                          {video.size && (
                            <span className="text-xs text-gray-500">
                              {formatFileSize(video.size)}
                            </span>
                          )}
                        </div>
                        <div className="flex items-center gap-1 text-xs text-gray-500">
                          <Clock className="h-3 w-3" />
                          {video.uploadedAt.toLocaleDateString()}
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeVideo(video.id);
                        }}
                        className="h-6 w-6 p-0 hover:bg-red-50 hover:text-red-600 flex-shrink-0"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Processing Status */}
      {isProcessing && (
        <div className="p-4 border-t border-gray-200 bg-blue-50">
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-sm text-blue-600">{t('processing_video')}</span>
          </div>
        </div>
      )}
    </div>
  );
}
