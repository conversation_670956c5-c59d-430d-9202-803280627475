import { NextRequest, NextResponse } from 'next/server';
import { getOpenRouterApi<PERSON>ey, isOpenRouterConfigured } from '@/lib/openrouter';
export const runtime = "edge";

export async function POST(req: NextRequest) {
  try {
    // Check API key configuration
    const apiKeyConfigured = isOpenRouterConfigured();
    console.log('OpenRouter API key configured:', apiKeyConfigured);

    // Verify that OpenRouter is configured
    if (!apiKeyConfigured) {
      console.error('OpenRouter API key not configured');
      return NextResponse.json(
        {
          error: 'OpenRouter API key not configured. Please add OPENROUTER_API_KEY to your .env.local file.'
        },
        { status: 500 }
      );
    }

    const openRouterApiKey = getOpenRouterApiKey();

    // Parse request body
    const body = await req.json();
    const { model, messages, plugins, stream = true, max_tokens } = body;

    console.log('Processing request:', {
      model,
      messageCount: messages.length,
      hasPlugins: !!plugins,
      stream,
      max_tokens
    });

    // Prepare the request to OpenRouter
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openRouterApiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
        'X-Title': 'AI Chat App'
      },
      body: JSON.stringify({
        model,
        messages,
        plugins,
        stream,
        max_tokens
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error from OpenRouter API:', errorData);
      return NextResponse.json(
        { error: 'Error from OpenRouter API', details: errorData },
        { status: response.status }
      );
    }

    // Handle streaming response
    if (stream) {
      const readableStream = new ReadableStream({
        async start(controller) {
          const reader = response.body?.getReader();
          if (!reader) {
            controller.close();
            return;
          }

          let buffer = '';

          try {
            while (true) {
              const { done, value } = await reader.read();
              if (done) {
                controller.close();
                break;
              }

              // Add new chunk to buffer
              buffer += new TextDecoder().decode(value);

              // Process complete lines from buffer
              const lines = buffer.split('\n');
              // Keep the last incomplete line in buffer
              buffer = lines.pop() || '';

              for (const line of lines) {
                if (line.startsWith('data: ')) {
                  const data = line.slice(6);
                  if (data === '[DONE]') {
                    controller.enqueue(new TextEncoder().encode(`data: [DONE]\n\n`));
                    continue;
                  }

                  try {
                    // Validate JSON before forwarding
                    const parsed = JSON.parse(data);
                    console.log('Streaming chunk:', parsed);

                    // Check for image data in the response
                    if (parsed.choices?.[0]?.delta?.image_data) {
                      console.log('Received image data in stream');
                    }

                    // Forward valid JSON data
                    controller.enqueue(new TextEncoder().encode(`data: ${data}\n\n`));
                  } catch (e) {
                    console.error('Error parsing chunk:', e, 'Data:', data);
                    // Skip invalid JSON chunks instead of forwarding them
                  }
                } else if (line.trim() === '') {
                  // Forward empty lines to maintain SSE format
                  controller.enqueue(new TextEncoder().encode('\n'));
                }
              }
            }
          } catch (error) {
            console.error('Error streaming response:', error);
            controller.error(error);
          }
        }
      });

      return new Response(readableStream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
      });
    }

    // Handle non-streaming response
    const data = await response.json();
    console.log('Non-streaming response:', data);

    // Check for image data in the response
    if (data.choices?.[0]?.message?.image_data) {
      console.log('Received image data in non-streaming response');
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error processing request:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: (error as Error).message },
      { status: 500 }
    );
  }
}