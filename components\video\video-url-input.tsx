'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Link, Youtube, Video } from 'lucide-react';
import { VideoData } from '@/types/video.d';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

interface VideoUrlInputProps {
  onVideoSelect: (video: VideoData) => void;
}

export default function VideoUrlInput({ onVideoSelect }: VideoUrlInputProps) {
  const t = useTranslations('video');
  const [urlInput, setUrlInput] = useState('');
  const [urlType, setUrlType] = useState<'youtube' | 'direct' | 'unknown'>('unknown');

  // YouTube URL 检测和转换
  const detectUrlType = (url: string): 'youtube' | 'direct' | 'unknown' => {
    try {
      const urlObj = new URL(url);
      
      // 检测 YouTube
      if (urlObj.hostname.includes('youtube.com') || urlObj.hostname.includes('youtu.be')) {
        return 'youtube';
      }
      
      // 检测直接视频文件
      const pathname = urlObj.pathname.toLowerCase();
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v'];
      if (videoExtensions.some(ext => pathname.endsWith(ext))) {
        return 'direct';
      }
      
      // 其他视频平台或流媒体
      const videoPlatforms = ['vimeo.com', 'dailymotion.com', 'twitch.tv', 'bilibili.com'];
      if (videoPlatforms.some(platform => urlObj.hostname.includes(platform))) {
        return 'direct';
      }
      
      return 'unknown';
    } catch {
      return 'unknown';
    }
  };

  // YouTube URL 转换
  const convertYouTubeUrl = (url: string): string => {
    try {
      const urlObj = new URL(url);
      
      // 处理 youtube.com/watch?v= 格式
      if (urlObj.hostname.includes('youtube.com') && urlObj.pathname === '/watch') {
        const videoId = urlObj.searchParams.get('v');
        if (videoId) {
          return `https://www.youtube-nocookie.com/embed/${videoId}?enablejsapi=1&rel=0&modestbranding=1`;
        }
      }
      
      // 处理 youtu.be/ 格式
      if (urlObj.hostname.includes('youtu.be')) {
        const videoId = urlObj.pathname.slice(1); // 移除开头的 /
        if (videoId) {
          return `https://www.youtube-nocookie.com/embed/${videoId}?enablejsapi=1&rel=0&modestbranding=1`;
        }
      }
      
      // 处理 youtube.com/embed/ 格式（已经是嵌入格式）
      if (urlObj.hostname.includes('youtube.com') && urlObj.pathname.startsWith('/embed/')) {
        return url.replace('youtube.com', 'youtube-nocookie.com');
      }
      
      return url;
    } catch {
      return url;
    }
  };

  // 获取 YouTube 视频ID
  const getYouTubeVideoId = (url: string): string | null => {
    try {
      const urlObj = new URL(url);
      
      if (urlObj.hostname.includes('youtube.com') && urlObj.pathname === '/watch') {
        return urlObj.searchParams.get('v');
      }
      
      if (urlObj.hostname.includes('youtu.be')) {
        return urlObj.pathname.slice(1);
      }
      
      if (urlObj.hostname.includes('youtube.com') && urlObj.pathname.startsWith('/embed/')) {
        return urlObj.pathname.split('/embed/')[1]?.split('?')[0];
      }
      
      return null;
    } catch {
      return null;
    }
  };

  // URL 输入变化处理
  const handleUrlChange = (value: string) => {
    setUrlInput(value);
    if (value.trim()) {
      const type = detectUrlType(value);
      setUrlType(type);
    } else {
      setUrlType('unknown');
    }
  };

  // 验证URL
  const validateVideoUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  // 提交URL
  const handleUrlSubmit = () => {
    if (!urlInput || !validateVideoUrl(urlInput)) {
      toast.error('Please enter a valid video URL');
      return;
    }

    let finalUrl = urlInput;
    let videoName = 'Video from URL';
    let isYouTube = false;

    // 根据URL类型处理
    switch (urlType) {
      case 'youtube':
        finalUrl = convertYouTubeUrl(urlInput);
        isYouTube = true;
        const videoId = getYouTubeVideoId(urlInput);
        videoName = videoId ? `YouTube Video (${videoId})` : 'YouTube Video';
        console.log('YouTube URL converted:', { original: urlInput, converted: finalUrl });
        break;
        
      case 'direct':
        finalUrl = urlInput;
        videoName = urlInput.split('/').pop() || 'Direct Video';
        console.log('Direct video URL:', finalUrl);
        break;
        
      default:
        finalUrl = urlInput;
        videoName = 'Video from URL';
        console.log('Unknown URL type, using as-is:', finalUrl);
    }

    const newVideo: VideoData = {
      id: Date.now().toString(),
      name: videoName,
      url: finalUrl,
      type: 'url',
      uploadedAt: new Date(),
    };

    onVideoSelect(newVideo);
    setUrlInput('');
    setUrlType('unknown');
    
    // 成功提示
    const successMessage = isYouTube 
      ? 'YouTube video loaded successfully' 
      : urlType === 'direct' 
        ? 'Video URL loaded successfully'
        : 'URL loaded successfully';
    
    toast.success(successMessage);
  };

  // 获取URL类型的显示信息
  const getUrlTypeInfo = () => {
    switch (urlType) {
      case 'youtube':
        return {
          icon: <Youtube className="h-4 w-4 text-red-600" />,
          label: 'YouTube',
          color: 'bg-red-50 text-red-700 border-red-200'
        };
      case 'direct':
        return {
          icon: <Video className="h-4 w-4 text-green-600" />,
          label: 'Direct Video',
          color: 'bg-green-50 text-green-700 border-green-200'
        };
      default:
        return {
          icon: <Link className="h-4 w-4 text-gray-600" />,
          label: 'URL',
          color: 'bg-gray-50 text-gray-700 border-gray-200'
        };
    }
  };

  const urlTypeInfo = getUrlTypeInfo();

  return (
    <div className="h-full flex flex-col items-center justify-center space-y-6 p-6">
      <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
        <Link className="h-8 w-8 text-blue-600" />
      </div>
      
      <div className="w-full max-w-md space-y-4">
        <div className="text-center">
          <h4 className="text-lg font-medium text-gray-900 mb-2">
            {t('enter_video_url')}
          </h4>
          <p className="text-sm text-gray-500 mb-4">
            {t('url_description')}
          </p>
        </div>
        
        <div className="space-y-3">
          {/* URL 输入框 */}
          <div className="relative">
            <input
              type="url"
              placeholder={t('video_url_placeholder')}
              value={urlInput}
              onChange={(e) => handleUrlChange(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleUrlSubmit()}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-20"
            />
            
            {/* URL 类型指示器 */}
            {urlInput && (
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                <Badge variant="outline" className={`text-xs ${urlTypeInfo.color}`}>
                  <span className="flex items-center gap-1">
                    {urlTypeInfo.icon}
                    {urlTypeInfo.label}
                  </span>
                </Badge>
              </div>
            )}
          </div>
          
          {/* 提交按钮 */}
          <Button
            onClick={handleUrlSubmit}
            disabled={!urlInput}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
          >
            {urlTypeInfo.icon}
            {t('load_video')}
          </Button>
        </div>
        
        {/* URL 类型说明 */}
        {urlInput && (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              {urlTypeInfo.icon}
              <span className="text-sm font-medium text-gray-700">
                {urlTypeInfo.label} Detected
              </span>
            </div>
            <p className="text-xs text-gray-600">
              {urlType === 'youtube' && 'Will be converted to embeddable format for playback'}
              {urlType === 'direct' && 'Will be played directly using HTML5 video player'}
              {urlType === 'unknown' && 'Will attempt to load as-is'}
            </p>
          </div>
        )}
        
        {/* 支持的格式说明 */}
        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500 mb-2">Supported formats:</p>
          <div className="flex flex-wrap justify-center gap-2">
            <Badge variant="outline" className="text-xs bg-red-50 text-red-700 border-red-200">
              <Youtube className="h-3 w-3 mr-1" />
              YouTube
            </Badge>
            <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
              <Video className="h-3 w-3 mr-1" />
              MP4/WebM
            </Badge>
            <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
              <Link className="h-3 w-3 mr-1" />
              Direct Links
            </Badge>
          </div>
        </div>
      </div>
    </div>
  );
}
