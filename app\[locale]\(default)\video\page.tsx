import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import VideoPageClient from '@/components/video/video-page-client';

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('video');
  
  return {
    title: `${t('title')} - AI Video Processing`,
    description: t('description'),
    keywords: 'video processing, AI transcription, video chat, video analysis',
  };
}

export default async function VideoPage() {
  const t = await getTranslations('video');

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-3 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-bold text-gray-900">{t('title')}</h1>
            <p className="text-sm text-gray-600">{t('subtitle')}</p>
          </div>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-500">{t('powered_by_ai')}</span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 min-h-0">
        <VideoPageClient />
      </div>
    </div>
  );
}
