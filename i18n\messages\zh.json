{"metadata": {"title": "几小时内构建任何 AI SaaS 创业项目 | ShipAny", "description": "ShipAny 是一个用于构建 AI SaaS 创业项目的 NextJS 模板，提供各种模板和组件，帮助您快速启动。", "keywords": "Ship<PERSON>ny, AI SaaS 模板, NextJS 模板"}, "user": {"sign_in": "登录", "sign_out": "退出登录", "credits": "额度", "api_keys": "API 密钥", "my_orders": "我的订单", "user_center": "用户中心", "admin_system": "管理后台"}, "sign_modal": {"sign_in_title": "登录", "sign_in_description": "登录您的账户", "sign_up_title": "注册", "sign_up_description": "创建新账户", "email_title": "邮箱", "email_placeholder": "请输入您的邮箱", "password_title": "密码", "password_placeholder": "请输入您的密码", "forgot_password": "忘记密码？", "or": "或", "continue": "继续", "no_account": "还没有账户？", "email_sign_in": "使用邮箱登录", "google_sign_in": "使用 Google 登录", "github_sign_in": "使用 GitHub 登录", "close_title": "关闭", "cancel_title": "取消"}, "my_orders": {"title": "我的订单", "description": "在 ShipAny 上购买的订单。", "no_orders": "未找到订单", "tip": "", "activate_order": "激活订单", "actived": "已激活", "join_discord": "加入 Discord", "read_docs": "阅读文档", "table": {"order_no": "订单号", "email": "邮箱", "product_name": "产品名称", "amount": "金额", "paid_at": "支付时间", "github_username": "GitHub 用户名", "status": "状态"}}, "my_credits": {"title": "我的积分", "left_tip": "剩余积分: {left_credits}", "no_credits": "没有积分记录", "recharge": "充值", "table": {"trans_no": "交易号", "trans_type": "交易类型", "credits": "积分", "updated_at": "更新时间", "status": "状态"}}, "api_keys": {"title": "API 密钥", "tip": "请妥善保管您的 API 密钥，避免泄露", "no_api_keys": "没有 API 密钥", "create_api_key": "创建 API 密钥", "table": {"name": "名称", "key": "密钥", "created_at": "创建时间"}, "form": {"name": "名称", "name_placeholder": "API 密钥名称", "submit": "提交"}}, "blog": {"title": "博客", "description": "关于 ShipAny 的新闻、资源和更新", "read_more_text": "阅读更多"}, "my_invites": {"title": "我的邀请", "description": "查看您的邀请记录", "no_invites": "未找到邀请记录", "my_invite_link": "我的邀请链接", "edit_invite_link": "编辑邀请链接", "copy_invite_link": "复制邀请链接", "invite_code": "邀请码", "invite_tip": "每邀请 1 位朋友购买 ShipAny，奖励 $50。", "invite_balance": "邀请奖励余额", "total_invite_count": "总邀请人数", "total_paid_count": "已充值人数", "total_award_amount": "总奖励金额", "update_invite_code": "设置邀请码", "update_invite_code_tip": "输入你的自定义邀请码", "update_invite_button": "保存", "no_orders": "你需要先购买过 ShipAny 才能邀请朋友", "no_affiliates": "你暂无邀请朋友的权限，请联系我们申请开通。", "table": {"invite_time": "邀请时间", "invite_user": "邀请用户", "status": "状态", "reward_percent": "奖励比例", "reward_amount": "奖励金额", "pending": "已注册，未支付", "completed": "已支付"}}, "feedback": {"title": "反馈", "description": "我们很乐意听取您对产品的看法或如何改进产品体验。", "submit": "提交", "loading": "提交中...", "contact_tip": "其他联系方式", "rating_tip": "您对 ShipAny 的看法如何？", "placeholder": "在这里留下您的反馈..."}, "voice": {"title": "语音转录", "description": "使用先进的AI语音识别技术将音频文件转换为文本", "upload_title": "音频输入", "upload_description": "上传音频/视频文件或提供URL进行转录。支持格式：MP3、WAV、M4A、OGG、FLAC、MP4（最大25MB）", "upload_file": "上传文件", "from_url": "从URL", "drop_text": "将音频/视频文件拖放到此处或点击浏览", "file_info": "支持MP3、WAV、M4A、OGG、FLAC、MP4格式，最大25MB", "url_placeholder": "输入音频/视频文件URL（例如：https://example.com/audio.mp3）", "url_description": "输入音频/视频文件的直接链接。预览将自动加载。支持格式：MP3、WAV、M4A、OGG、FLAC、MP4", "load_url": "加载URL", "refresh_preview": "刷新预览", "url_loaded": "URL加载成功", "invalid_url": "请输入有效的音频/视频URL", "start_transcription": "开始转录", "uploading": "上传中...", "clear": "清除", "audio_preview": "音频预览", "video_preview": "视频预览", "media_type": "媒体类型", "video": "视频", "audio": "音频", "file_name": "文件名", "file_size": "文件大小", "source_url": "源URL"}, "video": {"title": "AI视频处理", "subtitle": "上传视频并使用AI生成转录文本", "description": "使用AI驱动的转录和分析处理视频", "powered_by_ai": "AI驱动", "sidebar_title": "视频管理器", "sidebar_description": "上传和管理您的视频", "upload_file": "上传", "from_url": "URL", "drop_video_here": "拖拽视频到此处或点击浏览", "supported_formats": "MP4、AVI、MOV、WMV、FLV、WebM", "video_url_placeholder": "输入视频URL...", "add_video": "添加视频", "url_description": "视频文件直链（不支持YouTube链接）", "recent_videos": "最近视频", "no_videos_yet": "暂无视频", "upload_first_video": "上传您的第一个视频开始使用", "local_file": "本地", "url": "URL", "processing_video": "正在处理视频...", "no_video_selected": "未选择视频", "select_video_to_start": "从侧边栏选择一个视频开始", "generate_transcript": "生成转录", "processing": "处理中...", "starting_transcription": "开始转录...", "transcription_completed": "转录完成！", "transcription_failed": "转录失败", "transcript": "转录文本", "transcript_description": "AI生成的视频转录文本", "no_transcript_yet": "暂无转录文本", "generate_transcript_instruction": "点击「生成转录」从视频中提取文本", "auto_detected": "自动检测", "segments": "片段", "hide_timestamps": "隐藏时间", "show_timestamps": "显示时间", "edit": "编辑", "copy": "复制", "download": "下载", "save": "保存", "cancel": "取消", "search_transcript": "搜索转录文本...", "edit_transcript_placeholder": "在此编辑您的转录文本...", "full_text": "完整文本", "confidence": "置信度", "characters": "字符", "words": "词语", "search_results": "搜索结果", "transcript_copied": "转录文本已复制到剪贴板", "transcript_downloaded": "转录文本已下载", "transcript_saved": "转录文本已保存", "ai_assistant": "AI助手", "ai_description": "询问关于视频内容的问题", "video_loaded": "视频就绪", "no_video": "无视频", "transcript_ready": "转录就绪", "no_transcript": "无转录", "start_conversation": "开始对话", "ai_help_description": "我可以帮助您分析和理解视频内容", "suggested_questions": "建议问题", "what_is_main_topic": "这个视频的主要话题是什么？", "summarize_video": "你能总结一下这个视频吗？", "key_points": "讨论的要点是什么？", "explain_concept": "你能解释主要概念吗？", "message_copied": "消息已复制", "chat_cleared": "聊天已清空", "ask_about_video": "询问关于视频内容...", "upload_video_first": "请先上传视频并生成转录", "generate_transcript_to_chat": "生成转录文本以开始与AI聊天", "video_library": "视频库", "library_description": "您上传的视频和历史记录", "upload_video": "上传视频", "upload_video_description": "上传视频文件或输入URL开始使用", "or_click_to_browse": "或点击浏览", "max_size": "最大大小", "enter_video_url": "输入视频URL", "load_video": "加载视频", "timeline": "时间轴", "timeline_description": "点击任意片段跳转到视频中的对应时间"}}