'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  FileText,
  Lightbulb,
  Quote,
  Target,
  Twitter,
  Linkedin,
  Hash,
  BookOpen,
  Loader2,
  Copy,
  Check,
  Sparkles,
  Settings
} from 'lucide-react';
import { VideoData, TranscriptData } from '@/types/video.d';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';
import ModelSelector from './model-selector';

interface VideoSummaryProps {
  video: VideoData | null;
  transcript: TranscriptData | null;
}

type SummaryType = 'brief' | 'detailed' | 'quotes' | 'keypoints' | 'twitter' | 'linkedin' | 'thread' | 'blog';

interface SummaryData {
  type: SummaryType;
  content: string;
  isLoading: boolean;
  isGenerated: boolean;
}

export default function VideoSummary({ video, transcript }: VideoSummaryProps) {
  const t = useTranslations('video');

  const [activeTab, setActiveTab] = useState<SummaryType>('brief');
  const [copiedType, setCopiedType] = useState<SummaryType | null>(null);
  const [selectedModel, setSelectedModel] = useState('openai/gpt-4o-mini');
  const [summaries, setSummaries] = useState<Record<SummaryType, SummaryData>>({
    brief: { type: 'brief', content: '', isLoading: false, isGenerated: false },
    detailed: { type: 'detailed', content: '', isLoading: false, isGenerated: false },
    quotes: { type: 'quotes', content: '', isLoading: false, isGenerated: false },
    keypoints: { type: 'keypoints', content: '', isLoading: false, isGenerated: false },
    twitter: { type: 'twitter', content: '', isLoading: false, isGenerated: false },
    linkedin: { type: 'linkedin', content: '', isLoading: false, isGenerated: false },
    thread: { type: 'thread', content: '', isLoading: false, isGenerated: false },
    blog: { type: 'blog', content: '', isLoading: false, isGenerated: false },
  });

  // 标签页配置
  const tabs = [
    { id: 'brief', label: '简短摘要', icon: FileText, color: 'text-blue-600' },
    { id: 'detailed', label: '详细摘要', icon: BookOpen, color: 'text-green-600' },
    { id: 'quotes', label: '名言金句', icon: Quote, color: 'text-purple-600' },
    { id: 'keypoints', label: '要点总结', icon: Target, color: 'text-orange-600' },
    { id: 'twitter', label: 'Twitter帖子', icon: Twitter, color: 'text-sky-600' },
    { id: 'linkedin', label: 'LinkedIn文章', icon: Linkedin, color: 'text-blue-700' },
    { id: 'thread', label: 'Twitter主题', icon: Hash, color: 'text-indigo-600' },
    { id: 'blog', label: '博客文章', icon: Sparkles, color: 'text-pink-600' },
  ] as const;

  // 生成提示词
  const getPrompt = (type: SummaryType, transcriptText: string): string => {
    const prompts = {
      brief: `请为以下视频转录内容生成一个简短的摘要（100-150字）：\n\n${transcriptText}`,
      detailed: `请为以下视频转录内容生成一个详细的摘要（300-500字），包括主要观点、关键信息和结论：\n\n${transcriptText}`,
      quotes: `请从以下视频转录内容中提取5-8个最有价值的名言金句或重要观点：\n\n${transcriptText}`,
      keypoints: `请从以下视频转录内容中提取8-10个关键要点，用简洁的要点形式列出：\n\n${transcriptText}`,
      twitter: `请基于以下视频内容创建3个Twitter帖子创意（每个280字符以内），包括相关话题标签：\n\n${transcriptText}`,
      linkedin: `请基于以下视频内容创建一篇LinkedIn文章创意，包括标题、开头段落和主要观点：\n\n${transcriptText}`,
      thread: `请基于以下视频内容创建一个Twitter主题串（5-8条推文），每条推文简洁有力：\n\n${transcriptText}`,
      blog: `请基于以下视频内容创建一篇博客文章大纲，包括标题、副标题和主要段落结构：\n\n${transcriptText}`,
    };
    return prompts[type];
  };

  // 生成摘要
  const generateSummary = async (type: SummaryType) => {
    if (!transcript || !transcript.fullText) {
      toast.error('请先生成视频转录');
      return;
    }

    // 更新加载状态
    setSummaries(prev => ({
      ...prev,
      [type]: { ...prev[type], isLoading: true }
    }));

    try {
      const prompt = getPrompt(type, transcript.fullText);
      
      const response = await fetch('/api/ai/summary', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          type,
          model: selectedModel,
        }),
      });

      if (!response.ok) {
        throw new Error('生成摘要失败');
      }

      const data = await response.json();
      
      // 更新摘要内容
      setSummaries(prev => ({
        ...prev,
        [type]: {
          ...prev[type],
          content: data.content,
          isLoading: false,
          isGenerated: true
        }
      }));

      toast.success('摘要生成成功');
    } catch (error: any) {
      console.error('Summary generation error:', error);
      toast.error(error.message || '生成摘要失败');
      
      // 重置加载状态
      setSummaries(prev => ({
        ...prev,
        [type]: { ...prev[type], isLoading: false }
      }));
    }
  };

  // 复制内容
  const copyToClipboard = async (type: SummaryType) => {
    const content = summaries[type].content;
    if (!content) return;

    try {
      await navigator.clipboard.writeText(content);
      setCopiedType(type);
      toast.success('已复制到剪贴板');
      
      // 3秒后重置复制状态
      setTimeout(() => setCopiedType(null), 3000);
    } catch (error) {
      toast.error('复制失败');
    }
  };

  // 获取当前标签页信息
  const currentTab = tabs.find(tab => tab.id === activeTab);
  const currentSummary = summaries[activeTab];

  if (!video) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">视频总结</h3>
          <p className="text-sm text-gray-500">请先上传视频并生成转录</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* 标签页导航 */}
      <div className="border-b border-gray-200 bg-white">
        <div className="flex overflow-x-auto scrollbar-hide">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            const hasContent = summaries[tab.id as SummaryType].isGenerated;
            
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as SummaryType)}
                className={`flex items-center gap-2 px-4 py-3 text-sm font-medium border-b-2 transition-colors whitespace-nowrap ${
                  isActive
                    ? 'border-blue-500 text-blue-600 bg-blue-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Icon className={`h-4 w-4 ${isActive ? tab.color : 'text-gray-400'}`} />
                {tab.label}
                {hasContent && (
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                )}
              </button>
            );
          })}
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 p-6 overflow-y-auto">
        <Card>
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                {currentTab && <currentTab.icon className={`h-5 w-5 ${currentTab.color}`} />}
                {currentTab?.label}
              </CardTitle>
              <div className="flex items-center gap-2">
                {currentSummary.isGenerated && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(activeTab)}
                    className="flex items-center gap-2"
                  >
                    {copiedType === activeTab ? (
                      <Check className="h-4 w-4 text-green-600" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                    复制
                  </Button>
                )}
                <Button
                  onClick={() => generateSummary(activeTab)}
                  disabled={currentSummary.isLoading || !transcript}
                  size="sm"
                  className="flex items-center gap-2"
                >
                  {currentSummary.isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Sparkles className="h-4 w-4" />
                  )}
                  {currentSummary.isGenerated ? '重新生成' : '生成摘要'}
                </Button>
              </div>
            </div>

            {/* 模型选择器 */}
            <div className="mt-4">
              <div className="flex items-center gap-2 mb-2">
                <Settings className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">AI模型选择</span>
              </div>
              <ModelSelector
                selectedModel={selectedModel}
                onModelChange={setSelectedModel}
                disabled={currentSummary.isLoading}
              />
            </div>
          </CardHeader>
          
          <CardContent>
            {!transcript ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">请先生成视频转录</p>
              </div>
            ) : currentSummary.isLoading ? (
              <div className="text-center py-8">
                <Loader2 className="h-8 w-8 text-blue-600 animate-spin mx-auto mb-4" />
                <p className="text-gray-600">正在生成{currentTab?.label}...</p>
              </div>
            ) : currentSummary.content ? (
              <div className="prose prose-sm max-w-none">
                <div className="bg-gray-50 rounded-lg p-4 whitespace-pre-wrap">
                  {currentSummary.content}
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  {currentTab && <currentTab.icon className={`h-8 w-8 ${currentTab.color}`} />}
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  生成{currentTab?.label}
                </h3>
                <p className="text-gray-500 mb-4">
                  点击"生成摘要"按钮来创建{currentTab?.label}
                </p>
                <Button
                  onClick={() => generateSummary(activeTab)}
                  className="flex items-center gap-2"
                >
                  <Sparkles className="h-4 w-4" />
                  生成摘要
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
