'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ChevronDown, 
  Check, 
  Zap, 
  DollarSign, 
  Brain,
  Sparkles,
  Settings
} from 'lucide-react';
import { useTranslations } from 'next-intl';

interface ModelInfo {
  name: string;
  category: string;
  price: 'Low' | 'Medium' | 'High';
}

interface ModelSelectorProps {
  selectedModel: string;
  onModelChange: (model: string) => void;
  disabled?: boolean;
}

export default function ModelSelector({ selectedModel, onModelChange, disabled = false }: ModelSelectorProps) {
  const t = useTranslations('video');
  const [isOpen, setIsOpen] = useState(false);
  const [models, setModels] = useState<Record<string, ModelInfo>>({});
  const [loading, setLoading] = useState(true);

  // 获取模型列表
  useEffect(() => {
    const fetchModels = async () => {
      try {
        const response = await fetch('/api/ai/summary');
        if (response.ok) {
          const data = await response.json();
          setModels(data.models || {});
        }
      } catch (error) {
        console.error('Failed to fetch models:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchModels();
  }, []);

  const getPriceColor = (price: string) => {
    switch (price) {
      case 'Low': return 'bg-green-100 text-green-800 border-green-200';
      case 'Medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'High': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriceIcon = (price: string) => {
    switch (price) {
      case 'Low': return <DollarSign className="h-3 w-3" />;
      case 'Medium': return <Zap className="h-3 w-3" />;
      case 'High': return <Sparkles className="h-3 w-3" />;
      default: return <DollarSign className="h-3 w-3" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'OpenAI': return '🤖';
      case 'Anthropic': return '🧠';
      case 'Google': return '🔍';
      case 'Meta': return '🦙';
      case 'Mistral': return '🌪️';
      default: return '🤖';
    }
  };

  const selectedModelInfo = models[selectedModel];

  if (loading) {
    return (
      <div className="flex items-center gap-2 px-3 py-2 bg-gray-100 rounded-lg">
        <Settings className="h-4 w-4 animate-spin text-gray-500" />
        <span className="text-sm text-gray-600">Loading models...</span>
      </div>
    );
  }

  return (
    <div className="relative">
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled}
        className="w-full justify-between h-auto p-3 hover:bg-gray-50"
      >
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <span className="text-lg">{getCategoryIcon(selectedModelInfo?.category || '')}</span>
            <div className="text-left">
              <div className="font-medium text-sm">
                {selectedModelInfo?.name || selectedModel}
              </div>
              <div className="text-xs text-gray-500">
                {selectedModelInfo?.category || 'Unknown'}
              </div>
            </div>
          </div>
          {selectedModelInfo && (
            <Badge variant="outline" className={`text-xs ${getPriceColor(selectedModelInfo.price)}`}>
              <span className="flex items-center gap-1">
                {getPriceIcon(selectedModelInfo.price)}
                {selectedModelInfo.price}
              </span>
            </Badge>
          )}
        </div>
        <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </Button>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto">
          <div className="p-2">
            <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide px-2 py-1 mb-2">
              选择AI模型
            </div>
            
            {Object.entries(models).map(([modelId, modelInfo]) => (
              <button
                key={modelId}
                onClick={() => {
                  onModelChange(modelId);
                  setIsOpen(false);
                }}
                className={`w-full flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors ${
                  selectedModel === modelId ? 'bg-blue-50 border border-blue-200' : ''
                }`}
              >
                <div className="flex items-center gap-3">
                  <span className="text-lg">{getCategoryIcon(modelInfo.category)}</span>
                  <div className="text-left">
                    <div className="font-medium text-sm">{modelInfo.name}</div>
                    <div className="text-xs text-gray-500">{modelInfo.category}</div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className={`text-xs ${getPriceColor(modelInfo.price)}`}>
                    <span className="flex items-center gap-1">
                      {getPriceIcon(modelInfo.price)}
                      {modelInfo.price}
                    </span>
                  </Badge>
                  {selectedModel === modelId && (
                    <Check className="h-4 w-4 text-blue-600" />
                  )}
                </div>
              </button>
            ))}
          </div>
          
          {/* 模型说明 */}
          <div className="border-t border-gray-200 p-3 bg-gray-50">
            <div className="text-xs text-gray-600 space-y-1">
              <div className="flex items-center gap-2">
                <DollarSign className="h-3 w-3 text-green-600" />
                <span><strong>Low:</strong> 经济实惠，适合简单总结</span>
              </div>
              <div className="flex items-center gap-2">
                <Zap className="h-3 w-3 text-yellow-600" />
                <span><strong>Medium:</strong> 平衡性能和成本</span>
              </div>
              <div className="flex items-center gap-2">
                <Sparkles className="h-3 w-3 text-red-600" />
                <span><strong>High:</strong> 最佳质量，适合专业内容</span>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* 点击外部关闭 */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
