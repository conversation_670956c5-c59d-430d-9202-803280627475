import { NextRequest, NextResponse } from 'next/server';
import { getReplicateApiKey, isReplicateConfigured } from '@/lib/replicate';
import { respData, respErr } from '@/lib/resp';

export const runtime = "edge";

// Whisper model for speech-to-text
const WHISPER_MODEL = "vaibhavs10/incredibly-fast-whisper:3ab86df6c8f54c11309d4d1f930ac292bad43ace52d10c80d87eb258b3c9f79c";

export async function POST(req: NextRequest) {
  try {
    // Check API key configuration
    const apiKeyConfigured = isReplicateConfigured();
    console.log('Replicate API key configured:', apiKeyConfigured);

    if (!apiKeyConfigured) {
      console.error('Replicate API key not configured');
      return NextResponse.json(
        {
          error: 'Replicate API key not configured. Please add REPLICATE_API_TOKEN to your .env.local file.'
        },
        { status: 500 }
      );
    }

    const replicateApiKey = getReplicateApiKey();

    // Parse request body
    const body = await req.json();
    const { audio, batch_size = 64, timestamp = 'chunk', diarize_audio = false } = body;

    if (!audio) {
      return respErr('Audio file is required');
    }

    console.log('Processing voice transcription request:', {
      hasAudio: !!audio,
      batch_size,
      timestamp,
      diarize_audio
    });

    // Prepare the request to Replicate
    const response = await fetch('https://api.replicate.com/v1/predictions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${replicateApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        version: WHISPER_MODEL.split(':')[1],
        input: {
          audio,
          batch_size,
          timestamp,
          diarize_audio
        }
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error from Replicate API:', errorData);
      return NextResponse.json(
        { error: 'Error from Replicate API', details: errorData },
        { status: response.status }
      );
    }

    const prediction = await response.json();
    console.log('Prediction created:', prediction);

    return NextResponse.json({
      id: prediction.id,
      status: prediction.status,
      input: prediction.input,
      output: prediction.output,
      error: prediction.error,
      logs: prediction.logs,
      created_at: prediction.created_at,
      started_at: prediction.started_at,
      completed_at: prediction.completed_at
    });

  } catch (error) {
    console.error('Error processing voice transcription request:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: (error as Error).message },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const predictionId = searchParams.get('id');

    if (!predictionId) {
      return respErr('Prediction ID is required');
    }

    // Check API key configuration
    const apiKeyConfigured = isReplicateConfigured();
    if (!apiKeyConfigured) {
      return NextResponse.json(
        { error: 'Replicate API key not configured' },
        { status: 500 }
      );
    }

    const replicateApiKey = getReplicateApiKey();

    // Get prediction status
    const response = await fetch(`https://api.replicate.com/v1/predictions/${predictionId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${replicateApiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error fetching prediction:', errorData);
      return NextResponse.json(
        { error: 'Error fetching prediction', details: errorData },
        { status: response.status }
      );
    }

    const prediction = await response.json();
    
    return NextResponse.json({
      id: prediction.id,
      status: prediction.status,
      input: prediction.input,
      output: prediction.output,
      error: prediction.error,
      logs: prediction.logs,
      created_at: prediction.created_at,
      started_at: prediction.started_at,
      completed_at: prediction.completed_at
    });

  } catch (error) {
    console.error('Error fetching prediction status:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: (error as Error).message },
      { status: 500 }
    );
  }
}
