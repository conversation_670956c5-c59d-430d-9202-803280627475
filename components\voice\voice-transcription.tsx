'use client';

import React, { useState, useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Upload, Play, Pause, Download, Copy, Trash2, FileAudio } from 'lucide-react';
import { VoiceTranscriptionStatus, TranscriptionSegment } from '@/types/voice';
import { toast } from 'sonner';
import { useTranslations } from 'next-intl';

interface VoiceTranscriptionProps {
  onTranscriptionComplete?: (result: VoiceTranscriptionStatus) => void;
}

export default function VoiceTranscription({ onTranscriptionComplete }: VoiceTranscriptionProps) {
  const t = useTranslations('voice');
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [transcriptionStatus, setTranscriptionStatus] = useState<VoiceTranscriptionStatus | null>(null);
  const [isPolling, setIsPolling] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      // Check file type
      const allowedTypes = ['audio/mp3', 'audio/wav', 'audio/m4a', 'audio/ogg', 'audio/flac'];
      if (!allowedTypes.includes(selectedFile.type)) {
        toast.error('Please select a valid audio file (MP3, WAV, M4A, OGG, FLAC)');
        return;
      }

      // Check file size (max 25MB)
      if (selectedFile.size > 25 * 1024 * 1024) {
        toast.error('File size must be less than 25MB');
        return;
      }

      setFile(selectedFile);
      setTranscriptionStatus(null);
      
      // Create audio URL for preview
      const url = URL.createObjectURL(selectedFile);
      setAudioUrl(url);
    }
  }, []);

  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const droppedFile = event.dataTransfer.files[0];
    if (droppedFile) {
      const fakeEvent = {
        target: { files: [droppedFile] }
      } as React.ChangeEvent<HTMLInputElement>;
      handleFileSelect(fakeEvent);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  }, []);

  const convertFileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  const startTranscription = async () => {
    if (!file) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Convert file to base64
      const base64Audio = await convertFileToBase64(file);
      
      setUploadProgress(50);

      const response = await fetch('/api/voice', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          audio: base64Audio,
          batch_size: 64,
          timestamp: 'chunk',
          diarize_audio: false
        }),
      });

      setUploadProgress(100);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start transcription');
      }

      const result = await response.json();
      setTranscriptionStatus(result);
      
      if (result.status === 'starting' || result.status === 'processing') {
        startPolling(result.id);
      }

      toast.success('Transcription started successfully');
    } catch (error) {
      console.error('Error starting transcription:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to start transcription');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const startPolling = (predictionId: string) => {
    setIsPolling(true);
    
    pollingIntervalRef.current = setInterval(async () => {
      try {
        const response = await fetch(`/api/voice?id=${predictionId}`);
        if (response.ok) {
          const result = await response.json();
          setTranscriptionStatus(result);
          
          if (result.status === 'succeeded' || result.status === 'failed' || result.status === 'canceled') {
            stopPolling();
            if (result.status === 'succeeded') {
              toast.success('Transcription completed successfully');
              onTranscriptionComplete?.(result);
            } else {
              toast.error(`Transcription ${result.status}: ${result.error || 'Unknown error'}`);
            }
          }
        }
      } catch (error) {
        console.error('Error polling transcription status:', error);
      }
    }, 2000);
  };

  const stopPolling = () => {
    setIsPolling(false);
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
  };

  const handleAudioPlay = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleAudioTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleAudioLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  const downloadTranscription = () => {
    if (!transcriptionStatus?.output?.text) return;
    
    const blob = new Blob([transcriptionStatus.output.text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `transcription-${Date.now()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const clearAll = () => {
    setFile(null);
    setTranscriptionStatus(null);
    setAudioUrl(null);
    setIsPlaying(false);
    setCurrentTime(0);
    setDuration(0);
    stopPolling();
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'succeeded': return 'bg-green-500';
      case 'failed': return 'bg-red-500';
      case 'canceled': return 'bg-gray-500';
      case 'processing': return 'bg-blue-500';
      case 'starting': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="space-y-6">
      {/* File Upload */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileAudio className="h-5 w-5" />
            {t('upload_title')}
          </CardTitle>
          <CardDescription>
            {t('upload_description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div
            className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-gray-400 transition-colors"
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-lg font-medium text-gray-900 mb-2">
              {file ? file.name : t('drop_text')}
            </p>
            <p className="text-sm text-gray-500">
              {file ? `${(file.size / 1024 / 1024).toFixed(2)} MB` : t('file_info')}
            </p>
            <input
              ref={fileInputRef}
              type="file"
              accept="audio/*"
              onChange={handleFileSelect}
              className="hidden"
            />
          </div>
          
          {file && (
            <div className="mt-4 flex gap-2">
              <Button onClick={startTranscription} disabled={isUploading || isPolling}>
                {isUploading ? t('uploading') : t('start_transcription')}
              </Button>
              <Button variant="outline" onClick={clearAll}>
                <Trash2 className="h-4 w-4 mr-2" />
                {t('clear')}
              </Button>
            </div>
          )}
          
          {isUploading && (
            <div className="mt-4">
              <Progress value={uploadProgress} className="w-full" />
              <p className="text-sm text-gray-500 mt-2">{t('uploading')} {uploadProgress}%</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Audio Preview */}
      {audioUrl && (
        <Card>
          <CardHeader>
            <CardTitle>{t('audio_preview')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={handleAudioPlay}
              >
                {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>
              <div className="flex-1">
                <div className="text-sm text-gray-500 mb-1">
                  {formatTime(currentTime)} / {formatTime(duration)}
                </div>
                <Progress value={(currentTime / duration) * 100 || 0} className="w-full" />
              </div>
            </div>
            <audio
              ref={audioRef}
              src={audioUrl}
              onTimeUpdate={handleAudioTimeUpdate}
              onLoadedMetadata={handleAudioLoadedMetadata}
              onEnded={() => setIsPlaying(false)}
              className="hidden"
            />
          </CardContent>
        </Card>
      )}

      {/* Transcription Status */}
      {transcriptionStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>{t('transcription_status')}</span>
              <Badge className={getStatusColor(transcriptionStatus.status)}>
                {transcriptionStatus.status}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-500">Prediction ID</p>
                <p className="font-mono text-sm">{transcriptionStatus.id}</p>
              </div>
              
              {isPolling && (
                <div>
                  <p className="text-sm text-gray-500 mb-2">{t('processing')}</p>
                  <Progress value={undefined} className="w-full" />
                </div>
              )}
              
              {transcriptionStatus.error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600">{transcriptionStatus.error}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Transcription Result */}
      {transcriptionStatus?.output?.text && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>{t('transcription_result')}</span>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(transcriptionStatus.output!.text)}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  {t('copy')}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={downloadTranscription}
                >
                  <Download className="h-4 w-4 mr-2" />
                  {t('download')}
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              value={transcriptionStatus.output.text}
              readOnly
              className="min-h-[200px] font-mono text-sm"
            />
          </CardContent>
        </Card>
      )}

      {/* Timeline Segments */}
      {transcriptionStatus?.output?.segments && transcriptionStatus.output.segments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>{t('timeline')}</CardTitle>
            <CardDescription>
              {t('timeline_description')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {transcriptionStatus.output.segments.map((segment: TranscriptionSegment, index: number) => (
                <div
                  key={segment.id || index}
                  className="p-3 border rounded-md hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => {
                    if (audioRef.current) {
                      audioRef.current.currentTime = segment.start;
                    }
                  }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant="outline">
                      {formatTime(segment.start)} - {formatTime(segment.end)}
                    </Badge>
                    <span className="text-xs text-gray-500">
                      {t('confidence')}: {(1 - segment.no_speech_prob).toFixed(2)}
                    </span>
                  </div>
                  <p className="text-sm">{segment.text}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
