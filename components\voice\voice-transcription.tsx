'use client';

import React, { useState, useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';

import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Upload, Play, Pause, Download, Copy, Trash2, FileAudio, Link, Globe } from 'lucide-react';
import { VoiceTranscriptionStatus, TranscriptionChunk, TranscriptionSegment } from '@/types/voice';
import { toast } from 'sonner';
import { useTranslations } from 'next-intl';

interface VoiceTranscriptionProps {
  onTranscriptionComplete?: (result: VoiceTranscriptionStatus) => void;
}

export default function VoiceTranscription({ onTranscriptionComplete }: VoiceTranscriptionProps) {
  const t = useTranslations('voice');
  const [file, setFile] = useState<File | null>(null);
  const [audioUrlInput, setAudioUrlInput] = useState<string>('');
  const [inputMethod, setInputMethod] = useState<'file' | 'url'>('file');
  const [task, setTask] = useState<'transcribe' | 'translate'>('transcribe');
  const [language, setLanguage] = useState<string>('auto');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [transcriptionStatus, setTranscriptionStatus] = useState<VoiceTranscriptionStatus | null>(null);
  const [isPolling, setIsPolling] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [mediaType, setMediaType] = useState<'audio' | 'video' | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      // Check file type
      const allowedTypes = ['audio/mp4', 'audio/mp3', 'audio/wav', 'audio/m4a', 'audio/ogg', 'audio/flac', 'video/mp4'];
      if (!allowedTypes.includes(selectedFile.type)) {
        toast.error('Please select a valid audio/video file (MP3, WAV, M4A, OGG, FLAC, MP4)');
        return;
      }

      // Check file size (max 25MB)
      if (selectedFile.size > 25 * 1024 * 1024) {
        toast.error('File size must be less than 25MB');
        return;
      }

      setFile(selectedFile);
      setTranscriptionStatus(null);
      setInputMethod('file');

      // Determine media type and create URL for preview
      const isVideo = selectedFile.type.startsWith('video/');
      setMediaType(isVideo ? 'video' : 'audio');
      const url = URL.createObjectURL(selectedFile);
      setAudioUrl(url);
    }
  }, []);

  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const droppedFile = event.dataTransfer.files[0];
    if (droppedFile) {
      // Directly handle the dropped file instead of creating a fake event
      if (droppedFile) {
        // Check file type
        const allowedTypes = ['audio/mp4', 'audio/mp3', 'audio/wav', 'audio/m4a', 'audio/ogg', 'audio/flac', 'video/mp4'];
        if (!allowedTypes.includes(droppedFile.type)) {
          toast.error('Please select a valid audio/video file (MP3, WAV, M4A, OGG, FLAC, MP4)');
          return;
        }

        // Check file size (max 25MB)
        if (droppedFile.size > 25 * 1024 * 1024) {
          toast.error('File size must be less than 25MB');
          return;
        }

        setFile(droppedFile);
        setTranscriptionStatus(null);
        setInputMethod('file');

        // Determine media type and create URL for preview
        const isVideo = droppedFile.type.startsWith('video/');
        setMediaType(isVideo ? 'video' : 'audio');
        const url = URL.createObjectURL(droppedFile);
        setAudioUrl(url);
      }
    }
  }, []);

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  }, []);

  const validateAudioUrl = (url: string): boolean => {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname.toLowerCase();
      const validExtensions = ['.mp4', '.mp3', '.wav', '.m4a', '.ogg', '.flac'];
      return validExtensions.some(ext => pathname.endsWith(ext)) ||
             url.includes('audio') ||
             url.includes('video') ||
             url.includes('sound');
    } catch {
      return false;
    }
  };

  const handleUrlInput = useCallback((url: string) => {
    setAudioUrlInput(url);
    if (url && validateAudioUrl(url)) {
      setInputMethod('url');
      setFile(null);
      setTranscriptionStatus(null);

      // Determine media type from URL
      const pathname = url.toLowerCase();
      const isVideo = pathname.includes('.mp4') || pathname.includes('video');
      setMediaType(isVideo ? 'video' : 'audio');
    }
  }, []);

  const convertFileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  const startTranscription = async () => {
    if (!file && !audioUrlInput) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      let audioData: string;

      if (inputMethod === 'file' && file) {
        // Convert file to base64
        audioData = await convertFileToBase64(file);
        setUploadProgress(50);
      } else if (inputMethod === 'url' && audioUrlInput) {
        // Use URL directly
        if (!validateAudioUrl(audioUrlInput)) {
          throw new Error('Please enter a valid audio URL');
        }
        audioData = audioUrlInput;
        setUploadProgress(50);
      } else {
        throw new Error('Please select a file or enter a URL');
      }

      const response = await fetch('/api/voice', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          audio: audioData,
          task: task,
          batch_size: 64,
          return_timestamps: true,
          language: language === 'auto' ? null : language
        }),
      });

      setUploadProgress(100);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start transcription');
      }

      const result = await response.json();
      setTranscriptionStatus(result);

      if (result.status === 'starting' || result.status === 'processing') {
        startPolling(result.id);
      }

      toast.success('Transcription started successfully');
    } catch (error) {
      console.error('Error starting transcription:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to start transcription');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const startPolling = (predictionId: string) => {
    setIsPolling(true);
    
    pollingIntervalRef.current = setInterval(async () => {
      try {
        const response = await fetch(`/api/voice?id=${predictionId}`);
        if (response.ok) {
          const result = await response.json();
          setTranscriptionStatus(result);
          
          if (result.status === 'succeeded' || result.status === 'failed' || result.status === 'canceled') {
            stopPolling();
            if (result.status === 'succeeded') {
              toast.success('Transcription completed successfully');
              onTranscriptionComplete?.(result);
            } else {
              toast.error(`Transcription ${result.status}: ${result.error || 'Unknown error'}`);
            }
          }
        }
      } catch (error) {
        console.error('Error polling transcription status:', error);
      }
    }, 2000);
  };

  const stopPolling = () => {
    setIsPolling(false);
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
  };

  const handleAudioPlay = () => {
    const mediaElement = mediaType === 'video' ? videoRef.current : audioRef.current;
    if (mediaElement) {
      if (isPlaying) {
        mediaElement.pause();
      } else {
        mediaElement.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleAudioTimeUpdate = () => {
    const mediaElement = mediaType === 'video' ? videoRef.current : audioRef.current;
    if (mediaElement) {
      setCurrentTime(mediaElement.currentTime);
    }
  };

  const handleAudioLoadedMetadata = () => {
    const mediaElement = mediaType === 'video' ? videoRef.current : audioRef.current;
    if (mediaElement) {
      setDuration(mediaElement.duration);
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  const downloadTranscription = () => {
    if (!transcriptionStatus?.output?.text) return;
    
    const blob = new Blob([transcriptionStatus.output.text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `transcription-${Date.now()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const clearAll = () => {
    setFile(null);
    setAudioUrlInput('');
    setTranscriptionStatus(null);
    setAudioUrl(null);
    setMediaType(null);
    setIsPlaying(false);
    setCurrentTime(0);
    setDuration(0);
    setTask('transcribe');
    setLanguage('auto');
    stopPolling();
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'succeeded': return 'bg-green-500';
      case 'failed': return 'bg-red-500';
      case 'canceled': return 'bg-gray-500';
      case 'processing': return 'bg-blue-500';
      case 'starting': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="space-y-6">
      {/* Audio Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileAudio className="h-5 w-5" />
            {t('upload_title')}
          </CardTitle>
          <CardDescription>
            {t('upload_description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={inputMethod} onValueChange={(value) => setInputMethod(value as 'file' | 'url')}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="file" className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                {t('upload_file')}
              </TabsTrigger>
              <TabsTrigger value="url" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                {t('from_url')}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="file" className="mt-4">
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-gray-400 transition-colors"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p className="text-lg font-medium text-gray-900 mb-2">
                  {file ? file.name : t('drop_text')}
                </p>
                <p className="text-sm text-gray-500">
                  {file ? `${(file.size / 1024 / 1024).toFixed(2)} MB` : t('file_info')}
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="audio/*,video/mp4"
                  onChange={handleFileSelect}
                  className="hidden"
                />
              </div>
            </TabsContent>

            <TabsContent value="url" className="mt-4">
              <div className="space-y-4">
                <div className="flex gap-2">
                  <div className="flex-1">
                    <Input
                      type="url"
                      placeholder={t('url_placeholder')}
                      value={audioUrlInput}
                      onChange={(e) => handleUrlInput(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => {
                      if (audioUrlInput && validateAudioUrl(audioUrlInput)) {
                        setAudioUrl(audioUrlInput);
                        toast.success(t('url_loaded'));
                      } else {
                        toast.error(t('invalid_url'));
                      }
                    }}
                    disabled={!audioUrlInput}
                  >
                    <Link className="h-4 w-4 mr-2" />
                    {t('load_url')}
                  </Button>
                </div>
                <p className="text-sm text-gray-500">
                  {t('url_description')}
                </p>
              </div>
            </TabsContent>
          </Tabs>

          {/* Advanced Options */}
          <div className="mt-6 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="task-select">{t('task')}</Label>
                <Select value={task} onValueChange={(value) => setTask(value as 'transcribe' | 'translate')}>
                  <SelectTrigger id="task-select">
                    <SelectValue placeholder={t('select_task')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="transcribe">{t('transcribe')}</SelectItem>
                    <SelectItem value="translate">{t('translate')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="language-select">{t('language')}</Label>
                <Select value={language} onValueChange={setLanguage}>
                  <SelectTrigger id="language-select">
                    <SelectValue placeholder={t('auto_detect')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="auto">{t('auto_detect')}</SelectItem>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="zh">中文</SelectItem>
                    <SelectItem value="es">Español</SelectItem>
                    <SelectItem value="fr">Français</SelectItem>
                    <SelectItem value="de">Deutsch</SelectItem>
                    <SelectItem value="ja">日本語</SelectItem>
                    <SelectItem value="ko">한국어</SelectItem>
                    <SelectItem value="ru">Русский</SelectItem>
                    <SelectItem value="ar">العربية</SelectItem>
                    <SelectItem value="hi">हिन्दी</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="text-sm text-gray-500">
              <p><strong>{t('transcribe')}:</strong> {t('transcribe_description')}</p>
              <p><strong>{t('translate')}:</strong> {t('translate_description')}</p>
            </div>
          </div>

          {(file || (audioUrlInput && validateAudioUrl(audioUrlInput))) && (
            <div className="mt-4 flex gap-2">
              <Button onClick={startTranscription} disabled={isUploading || isPolling}>
                {isUploading ? t('uploading') : t('start_transcription')}
              </Button>
              <Button variant="outline" onClick={clearAll}>
                <Trash2 className="h-4 w-4 mr-2" />
                {t('clear')}
              </Button>
            </div>
          )}

          {isUploading && (
            <div className="mt-4">
              <Progress value={uploadProgress} className="w-full" />
              <p className="text-sm text-gray-500 mt-2">{t('uploading')} {uploadProgress}%</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Media Preview */}
      {audioUrl && (
        <Card>
          <CardHeader>
            <CardTitle>
              {mediaType === 'video' ? t('video_preview') : t('audio_preview')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Media Player */}
              {mediaType === 'video' ? (
                <div className="w-full">
                  <video
                    ref={videoRef}
                    src={audioUrl}
                    onTimeUpdate={handleAudioTimeUpdate}
                    onLoadedMetadata={handleAudioLoadedMetadata}
                    onEnded={() => setIsPlaying(false)}
                    className="w-full max-h-64 rounded-md bg-black"
                    controls
                    preload="metadata"
                  />
                </div>
              ) : (
                <audio
                  ref={audioRef}
                  src={audioUrl}
                  onTimeUpdate={handleAudioTimeUpdate}
                  onLoadedMetadata={handleAudioLoadedMetadata}
                  onEnded={() => setIsPlaying(false)}
                  className="hidden"
                />
              )}

              {/* Controls */}
              <div className="flex items-center gap-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleAudioPlay}
                >
                  {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                </Button>
                <div className="flex-1">
                  <div className="text-sm text-gray-500 mb-1">
                    {formatTime(currentTime)} / {formatTime(duration)}
                  </div>
                  <Progress value={(currentTime / duration) * 100 || 0} className="w-full" />
                </div>
              </div>

              {/* Media Info */}
              <div className="text-sm text-gray-500">
                <p><strong>{t('media_type')}:</strong> {mediaType === 'video' ? t('video') : t('audio')}</p>
                {file && (
                  <>
                    <p><strong>{t('file_name')}:</strong> {file.name}</p>
                    <p><strong>{t('file_size')}:</strong> {(file.size / 1024 / 1024).toFixed(2)} MB</p>
                  </>
                )}
                {inputMethod === 'url' && (
                  <p><strong>{t('source_url')}:</strong> <span className="break-all">{audioUrlInput}</span></p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Transcription Status */}
      {transcriptionStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>{t('transcription_status')}</span>
              <Badge className={getStatusColor(transcriptionStatus.status)}>
                {transcriptionStatus.status}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-500">Prediction ID</p>
                <p className="font-mono text-sm">{transcriptionStatus.id}</p>
              </div>
              
              {isPolling && (
                <div>
                  <p className="text-sm text-gray-500 mb-2">{t('processing')}</p>
                  <Progress value={undefined} className="w-full" />
                </div>
              )}
              
              {transcriptionStatus.error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600">{transcriptionStatus.error}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Transcription Result */}
      {transcriptionStatus?.output?.text && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>{t('transcription_result')}</span>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(transcriptionStatus.output!.text)}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  {t('copy')}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={downloadTranscription}
                >
                  <Download className="h-4 w-4 mr-2" />
                  {t('download')}
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              value={transcriptionStatus.output.text}
              readOnly
              className="min-h-[200px] font-mono text-sm"
            />
          </CardContent>
        </Card>
      )}

      {/* Timeline Chunks */}
      {transcriptionStatus?.output?.chunks && transcriptionStatus.output.chunks.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>{t('timeline')}</CardTitle>
            <CardDescription>
              {t('timeline_description')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {transcriptionStatus.output.chunks.map((chunk: TranscriptionChunk, index: number) => (
                <div
                  key={index}
                  className="p-3 border rounded-md hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => {
                    const mediaElement = mediaType === 'video' ? videoRef.current : audioRef.current;
                    if (mediaElement) {
                      mediaElement.currentTime = chunk.timestamp[0];
                    }
                  }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant="outline">
                      {formatTime(chunk.timestamp[0])} - {formatTime(chunk.timestamp[1])}
                    </Badge>
                    <span className="text-xs text-gray-500">
                      {t('duration')}: {(chunk.timestamp[1] - chunk.timestamp[0]).toFixed(1)}s
                    </span>
                  </div>
                  <p className="text-sm">{chunk.text}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
