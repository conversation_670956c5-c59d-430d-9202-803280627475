import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
  const openRouterApiKey = process.env.OPENROUTER_API_KEY;
  
  return NextResponse.json({
    hasOpenRouterKey: !!openRouterApiKey,
    keyLength: openRouterApiKey?.length || 0,
    keyPrefix: openRouterApiKey?.substring(0, 15) || 'none',
    allEnvKeys: Object.keys(process.env).filter(key => key.includes('OPENROUTER')),
  });
}
