'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';

export default function TestSummaryPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testEnv = async () => {
    try {
      const response = await fetch('/api/test-env');
      const data = await response.json();
      setResult({ type: 'env', data });
    } catch (error) {
      setResult({ type: 'error', data: error });
    }
  };

  const testSummary = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/ai/summary', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: '请为以下内容生成简短摘要：这是一个测试视频，内容包括AI技术的发展和应用。',
          type: 'brief',
          model: 'openai/gpt-4o-mini'
        })
      });
      
      const data = await response.json();
      setResult({ type: 'summary', data, status: response.status });
    } catch (error) {
      setResult({ type: 'error', data: error });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">API测试页面</h1>
      
      <div className="space-y-4 mb-6">
        <Button onClick={testEnv}>测试环境变量</Button>
        <Button onClick={testSummary} disabled={loading}>
          {loading ? '测试中...' : '测试摘要API'}
        </Button>
      </div>

      {result && (
        <div className="bg-gray-100 p-4 rounded-lg">
          <h3 className="font-semibold mb-2">测试结果:</h3>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
