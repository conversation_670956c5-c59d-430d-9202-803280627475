'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Clock,
  Share,
  Download
} from 'lucide-react';
import VideoFileUpload from './video-file-upload';
import VideoUrlInput from './video-url-input';
import { VideoData, TranscriptData } from '@/types/video';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

interface VideoPlayerProps {
  video: VideoData | null;
  onVideoSelect: (video: VideoData) => void;
  onTranscriptGenerated: (transcript: TranscriptData) => void;
  isProcessing: boolean;
  setIsProcessing: (processing: boolean) => void;
  transcript?: TranscriptData | null;
}

export default function SimpleVideoPlayer({ 
  video, 
  onVideoSelect,
  onTranscriptGenerated, 
  isProcessing, 
  setIsProcessing,
  transcript 
}: VideoPlayerProps) {
  const t = useTranslations('video');
  
  const [inputMethod, setInputMethod] = useState<'file' | 'url'>('file');
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);

  const videoRef = useRef<HTMLVideoElement>(null);





  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const handleSeek = (time: number) => {
    if (videoRef.current) {
      videoRef.current.currentTime = time;
      setCurrentTime(time);
    }
  };

  const startTranscription = async () => {
    if (!video) return;
    
    setIsProcessing(true);
    toast.info(t('starting_transcription'));
    
    try {
      const formData = new FormData();
      
      if (video.type === 'file' && video.file) {
        formData.append('video', video.file);
      } else {
        formData.append('video', video.url);
      }
      
      formData.append('task', 'transcribe');
      formData.append('language', 'None');
      formData.append('batch_size', '64');
      formData.append('return_timestamps', 'true');

      const response = await fetch('/api/video', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || errorData.error || 'Transcription failed');
      }

      const result = await response.json();
      
      const transcriptData: TranscriptData = {
        id: Date.now().toString(),
        videoId: video.id,
        segments: result.segments || [],
        fullText: result.text || '',
        language: result.language || 'auto-detected',
        generatedAt: new Date()
      };
      
      onTranscriptGenerated(transcriptData);
      toast.success(t('transcription_completed'));
    } catch (error: any) {
      console.error('Transcription error:', error);
      toast.error(error.message || t('transcription_failed'));
    } finally {
      setIsProcessing(false);
    }
  };

  if (!video) {
    return (
      <div className="h-full flex flex-col bg-white">
        {/* Upload Header */}
        <div className="bg-white px-4 py-3 border-b border-gray-200 flex-shrink-0">
          <h3 className="text-base font-medium text-gray-900">{t('upload_video')}</h3>
          <p className="text-sm text-gray-500 mt-1">{t('upload_video_description')}</p>
        </div>

        {/* Upload Tabs */}
        <div className="px-4 py-3 border-b border-gray-200 flex-shrink-0">
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            <button
              onClick={() => setInputMethod('file')}
              className={`flex-1 flex items-center justify-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                inputMethod === 'file'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              {t('upload_file')}
            </button>
            <button
              onClick={() => setInputMethod('url')}
              className={`flex-1 flex items-center justify-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                inputMethod === 'url'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
              {t('from_url')}
            </button>
          </div>
        </div>

        {/* Upload Content */}
        <div className="flex-1 overflow-hidden">
          {inputMethod === 'file' ? (
            <VideoFileUpload onVideoSelect={onVideoSelect} />
          ) : (
            <VideoUrlInput onVideoSelect={onVideoSelect} />
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white overflow-hidden">
      {/* Video Header - Compact */}
      <div className="bg-white px-4 py-2 border-b border-gray-200 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-semibold text-gray-900 truncate">{video.name}</h3>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs">
                {video.type === 'file' ? t('local_file') : t('url')}
              </Badge>
              {duration > 0 && (
                <span className="text-xs text-gray-500 flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {formatTime(duration)}
                </span>
              )}
              {video.size && (
                <span className="text-xs text-gray-500">
                  {(video.size / 1024 / 1024).toFixed(1)} MB
                </span>
              )}
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Button
              onClick={startTranscription}
              disabled={isProcessing}
              size="sm"
              className="bg-blue-600 hover:bg-blue-700 text-xs px-2 py-1"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                  {t('processing')}
                </>
              ) : (
                t('generate_transcript')
              )}
            </Button>
            <Button variant="outline" size="sm" className="h-7 w-7 p-0">
              <Share className="h-3 w-3" />
            </Button>
            <Button variant="outline" size="sm" className="h-7 w-7 p-0">
              <Download className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>

      {/* Video Player - Full Area */}
      <div className="bg-black relative flex-1 min-h-0">
        {video.url.includes('youtube-nocookie.com/embed') ? (
          // YouTube iframe player - Full size
          <iframe
            src={video.url}
            className="w-full h-full"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            title="YouTube video player"
          />
        ) : (
          // Regular video player - Full size with cover
          <video
            ref={videoRef}
            src={video.url}
            onTimeUpdate={handleTimeUpdate}
            onLoadedMetadata={handleLoadedMetadata}
            className="w-full h-full object-cover"
            controls
            preload="metadata"
            playsInline
            crossOrigin="anonymous"
          />
        )}
      </div>

      {/* Timeline and Transcript - Compact */}
      {transcript && (
        <div className="h-48 bg-white border-t border-gray-200 overflow-hidden flex-shrink-0">
          <div className="h-full flex flex-col">
            {/* Timeline Header */}
            <div className="px-4 py-1.5 border-b border-gray-200 bg-gray-50 flex-shrink-0">
              <h4 className="text-sm font-semibold text-gray-900 flex items-center gap-2">
                <Clock className="h-4 w-4" />
                {t('timeline')}
              </h4>
            </div>

            {/* Timeline Content */}
            <div className="flex-1 overflow-y-auto p-2">
              <div className="space-y-1">
                {transcript.segments.map((segment) => (
                  <div
                    key={segment.id}
                    className={`p-2 rounded-md border cursor-pointer transition-all duration-200 hover:bg-blue-50 hover:border-blue-200 ${
                      currentTime >= segment.start && currentTime <= segment.end
                        ? 'bg-blue-100 border-blue-300 shadow-sm'
                        : 'bg-white border-gray-200'
                    }`}
                    onClick={() => handleSeek(segment.start)}
                  >
                    <div className="flex items-start gap-2">
                      <div className="flex-shrink-0">
                        <Badge
                          variant="outline"
                          className={`text-xs px-1.5 py-0.5 ${
                            currentTime >= segment.start && currentTime <= segment.end
                              ? 'bg-blue-200 text-blue-800 border-blue-300'
                              : 'bg-gray-100 text-gray-600'
                          }`}
                        >
                          {formatTime(segment.start)}
                        </Badge>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-900 leading-relaxed">
                          {segment.text}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
