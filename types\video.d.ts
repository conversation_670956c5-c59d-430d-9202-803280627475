export interface VideoData {
  id: string;
  name: string;
  url: string;
  file?: File;
  duration?: number;
  size?: number;
  type: 'file' | 'url';
  thumbnail?: string;
  uploadedAt: Date;
}

export interface TranscriptSegment {
  id: string;
  start: number;
  end: number;
  text: string;
  confidence?: number;
}

export interface TranscriptData {
  id: string;
  videoId: string;
  segments: TranscriptSegment[];
  fullText: string;
  language?: string;
  generatedAt: Date;
}

export interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  relatedSegment?: string; // 关联的转录片段ID
}

export interface VideoProcessingStatus {
  status: 'idle' | 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  message?: string;
}

export interface VideoUploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface VideoAnalysisRequest {
  video: string | File;
  task?: 'transcribe' | 'analyze';
  language?: string;
  includeTimestamps?: boolean;
}

export interface VideoAnalysisResponse {
  transcript: TranscriptData;
  summary?: string;
  topics?: string[];
  keyPoints?: string[];
}
