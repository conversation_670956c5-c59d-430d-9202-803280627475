# Voice Transcription Feature

This document describes the voice transcription feature implementation using Replicate's Whisper model.

## Overview

The voice transcription feature allows users to upload audio files and convert them to text using advanced AI speech recognition technology. It supports multiple audio formats and provides detailed timestamps for each segment.

## Features

- **Multiple Input Methods**: Upload local files or provide direct URLs to audio/video files
- **Multiple Formats**: Support for MP3, WAV, M4A, OGG, FLAC audio and MP4 video files up to 25MB
- **URL Input Support**: Direct transcription from audio/video URLs without downloading
- **Transcription & Translation**: Convert speech to text in original language or translate to English
- **Language Detection**: Automatic language detection or manual language selection
- **Multi-language Support**: Support for 10+ languages including English, Chinese, Spanish, French, etc.
- **Real-time Processing**: Live status updates during transcription
- **Timeline View**: Detailed timestamps for each segment with clickable navigation
- **Audio/Video Preview**: Built-in media player with playback controls
- **Export Options**: Copy to clipboard or download as text file
- **Internationalization**: Support for English and Chinese languages
- **Responsive Design**: Works on desktop and mobile devices

## File Structure

```
app/
├── api/voice/route.ts              # API endpoint for voice transcription
├── [locale]/(default)/voice/       # Voice transcription page
│   └── page.tsx
components/
└── voice/
    └── voice-transcription.tsx     # Main transcription component
lib/
├── openrouter.ts                   # OpenRouter configuration
└── replicate.ts                    # Replicate configuration
types/
└── voice.d.ts                      # TypeScript type definitions
i18n/
├── messages/
│   ├── en.json                     # English translations
│   └── zh.json                     # Chinese translations
└── pages/landing/
    ├── en.json                     # English navigation
    └── zh.json                     # Chinese navigation
```

## Setup Instructions

### 1. Install Dependencies

```bash
pnpm add @radix-ui/react-progress replicate
```

### 2. Environment Variables

Add the following environment variables to your `.env.local` file:

```env
# Replicate API Token for Voice Transcription
# Get your token from https://replicate.com/
REPLICATE_API_TOKEN=r8_your_token_here
```

### 3. API Configuration

The API endpoint is configured to use Replicate's incredibly-fast-whisper model:
- Model: `vaibhavs10/incredibly-fast-whisper:3ab86df6c8f54c11309d4d1f930ac292bad43ace52d10c80d87eb258b3c9f79c`
- Supports batch processing with configurable batch size
- Provides timestamp generation and confidence scoring

## Usage

### Basic Usage

1. Navigate to `/voice` in your application
2. Choose your input method:
   - **File Upload**: Drag and drop an audio file or click to browse
   - **URL Input**: Enter a direct link to an audio file
3. Click "Start Transcription" to begin processing
4. Wait for the AI to process your audio file
5. View results with timeline, copy to clipboard, or download as text

### API Usage

#### Start Transcription

```javascript
// Using file upload (base64)
const response = await fetch('/api/voice', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    audio: base64AudioData, // base64 encoded file
    batch_size: 64,
    timestamp: 'chunk',
    diarize_audio: false
  }),
});

// Using URL input
const response = await fetch('/api/voice', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    audio: 'https://example.com/audio.mp3', // direct URL
    batch_size: 64,
    timestamp: 'chunk',
    diarize_audio: false
  }),
});

const result = await response.json();
```

#### Check Status

```javascript
const response = await fetch(`/api/voice?id=${predictionId}`);
const status = await response.json();
```

## Component Props

### VoiceTranscription Component

```typescript
interface VoiceTranscriptionProps {
  onTranscriptionComplete?: (result: VoiceTranscriptionStatus) => void;
}
```

## Type Definitions

### VoiceTranscriptionRequest

```typescript
interface VoiceTranscriptionRequest {
  audio: string | File | Blob;
  task?: 'transcribe' | 'translate';
  batch_size?: number;
  return_timestamps?: boolean;
  language?: string;
}
```

### VoiceTranscriptionResponse

```typescript
interface VoiceTranscriptionResponse {
  text: string;
  chunks?: TranscriptionChunk[];
  language?: string;
}
```

### TranscriptionChunk

```typescript
interface TranscriptionChunk {
  text: string;
  timestamp: [number, number]; // [start, end] in seconds
}
```

### TranscriptionSegment

```typescript
interface TranscriptionSegment {
  id: number;
  seek: number;
  start: number;
  end: number;
  text: string;
  tokens: number[];
  temperature: number;
  avg_logprob: number;
  compression_ratio: number;
  no_speech_prob: number;
}
```

## Error Handling

The implementation includes comprehensive error handling for:
- Invalid file types
- File size limits (25MB max)
- API configuration issues
- Network errors
- Processing failures

## Internationalization

The feature supports multiple languages through next-intl:
- English (`en`)
- Chinese (`zh`)

All user-facing text is internationalized and can be easily extended to support additional languages.

## Performance Considerations

- Files are converted to base64 for API transmission
- Progress indicators provide user feedback during upload
- Polling mechanism checks transcription status every 2 seconds
- Audio preview uses object URLs for efficient memory usage

## Security

- API keys are stored securely in environment variables
- File type validation prevents malicious uploads
- File size limits prevent abuse
- No audio data is stored permanently on the server

## Browser Compatibility

- Modern browsers with File API support
- Audio element support for preview functionality
- Drag and drop API support
- Clipboard API for copy functionality

## Troubleshooting

### Common Issues

1. **API Key Not Configured**
   - Ensure `REPLICATE_API_TOKEN` is set in your environment variables
   - Verify the token is valid and has sufficient credits

2. **File Upload Fails**
   - Check file format (must be MP3, WAV, M4A, OGG, or FLAC)
   - Verify file size is under 25MB
   - Ensure stable internet connection

3. **URL Input Issues**
   - Verify the URL is accessible and points to an audio file
   - Check that the URL has a valid audio file extension
   - Ensure the audio file is publicly accessible (no authentication required)
   - Try downloading the file manually to verify it's valid

4. **Transcription Stuck in Processing**
   - Large files may take several minutes to process
   - Check Replicate service status
   - Verify API token has sufficient credits

## Future Enhancements

Potential improvements for the voice transcription feature:
- Support for additional audio formats
- Real-time streaming transcription
- Speaker diarization options
- Custom model selection
- Batch processing for multiple files
- Integration with cloud storage services
