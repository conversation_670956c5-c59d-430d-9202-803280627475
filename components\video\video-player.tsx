'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  Settings,
  Download,
  Share,
  RotateCcw,
  FastForward,
  Upload,
  Link
} from 'lucide-react';
import { VideoData, TranscriptData } from '@/types/video';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

interface VideoPlayerProps {
  video: VideoData | null;
  onVideoSelect: (video: VideoData) => void;
  onTranscriptGenerated: (transcript: TranscriptData) => void;
  isProcessing: boolean;
  setIsProcessing: (processing: boolean) => void;
}

export default function VideoPlayer({
  video,
  onVideoSelect,
  onTranscriptGenerated,
  isProcessing,
  setIsProcessing
}: VideoPlayerProps) {
  const t = useTranslations('video');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [inputMethod, setInputMethod] = useState<'file' | 'url'>('file');
  const [urlInput, setUrlInput] = useState('');

  const videoRef = useRef<HTMLVideoElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (video && videoRef.current) {
      videoRef.current.load();
      setCurrentTime(0);
      setIsPlaying(false);
    }
  }, [video]);

  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };

  const handleVolumeChange = (newVolume: number) => {
    if (videoRef.current) {
      videoRef.current.volume = newVolume;
      setVolume(newVolume);
      setIsMuted(newVolume === 0);
    }
  };

  const handleMute = () => {
    if (videoRef.current) {
      const newMuted = !isMuted;
      videoRef.current.muted = newMuted;
      setIsMuted(newMuted);
    }
  };

  const handleSeek = (time: number) => {
    if (videoRef.current) {
      videoRef.current.currentTime = time;
      setCurrentTime(time);
    }
  };

  const handlePlaybackRateChange = (rate: number) => {
    if (videoRef.current) {
      videoRef.current.playbackRate = rate;
      setPlaybackRate(rate);
    }
  };

  const formatTime = (time: number) => {
    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = Math.floor(time % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const validateVideoUrl = (url: string): boolean => {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname.toLowerCase();
      const validExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'];
      return validExtensions.some(ext => pathname.endsWith(ext)) ||
             url.includes('video') ||
             url.includes('youtube') ||
             url.includes('vimeo');
    } catch {
      return false;
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      // Check file type
      const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/webm'];
      if (!allowedTypes.includes(selectedFile.type)) {
        toast.error('Please select a valid video file (MP4, AVI, MOV, WMV, FLV, WebM)');
        return;
      }

      // Check file size (max 100MB)
      if (selectedFile.size > 100 * 1024 * 1024) {
        toast.error('File size must be less than 100MB');
        return;
      }

      const newVideo: VideoData = {
        id: Date.now().toString(),
        name: selectedFile.name,
        url: URL.createObjectURL(selectedFile),
        file: selectedFile,
        size: selectedFile.size,
        type: 'file',
        uploadedAt: new Date(),
      };

      onVideoSelect(newVideo);
      toast.success('Video uploaded successfully');
    }
  };

  const handleUrlSubmit = () => {
    if (!urlInput || !validateVideoUrl(urlInput)) {
      toast.error('Please enter a valid video URL');
      return;
    }

    const newVideo: VideoData = {
      id: Date.now().toString(),
      name: urlInput.split('/').pop() || 'Video from URL',
      url: urlInput,
      type: 'url',
      uploadedAt: new Date(),
    };

    onVideoSelect(newVideo);
    setUrlInput('');
    toast.success('Video URL added successfully');
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const droppedFile = event.dataTransfer.files[0];
    if (droppedFile && droppedFile.type.startsWith('video/')) {
      const fakeEvent = {
        target: { files: [droppedFile] }
      } as React.ChangeEvent<HTMLInputElement>;
      handleFileSelect(fakeEvent);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const startTranscription = async () => {
    if (!video) return;
    
    setIsProcessing(true);
    toast.info(t('starting_transcription'));
    
    try {
      // TODO: 这里将来会调用实际的转录API
      // 模拟转录过程
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const mockTranscript: TranscriptData = {
        id: Date.now().toString(),
        videoId: video.id,
        segments: [
          {
            id: '1',
            start: 0,
            end: 5,
            text: 'Welcome to this video demonstration.',
            confidence: 0.95
          },
          {
            id: '2',
            start: 5,
            end: 12,
            text: 'In this video, we will explore various topics and concepts.',
            confidence: 0.92
          },
          {
            id: '3',
            start: 12,
            end: 18,
            text: 'Please follow along as we dive into the details.',
            confidence: 0.88
          }
        ],
        fullText: 'Welcome to this video demonstration. In this video, we will explore various topics and concepts. Please follow along as we dive into the details.',
        language: 'english',
        generatedAt: new Date()
      };
      
      onTranscriptGenerated(mockTranscript);
      toast.success(t('transcription_completed'));
    } catch (error) {
      toast.error(t('transcription_failed'));
      console.error('Transcription error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  if (!video) {
    return (
      <div className="h-full flex flex-col bg-white">
        {/* Upload Header */}
        <div className="bg-white px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">{t('upload_video')}</h3>
          <p className="text-sm text-gray-500 mt-1">{t('upload_video_description')}</p>
        </div>

        {/* Upload Area */}
        <div className="flex-1 p-6">
          <div className="h-full flex flex-col">
            {/* Upload Tabs */}
            <div className="mb-6">
              <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                <button
                  onClick={() => setInputMethod('file')}
                  className={`flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    inputMethod === 'file'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Upload className="h-4 w-4" />
                  {t('upload_file')}
                </button>
                <button
                  onClick={() => setInputMethod('url')}
                  className={`flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    inputMethod === 'url'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Link className="h-4 w-4" />
                  {t('from_url')}
                </button>
              </div>
            </div>

            {/* Upload Content */}
            <div className="flex-1">
              {inputMethod === 'file' ? (
                <div
                  className="h-full border-2 border-dashed border-gray-300 rounded-xl p-8 text-center cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 flex flex-col items-center justify-center"
                  onDrop={handleDrop}
                  onDragOver={handleDragOver}
                  onClick={() => fileInputRef.current?.click()}
                >
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <Upload className="h-8 w-8 text-blue-600" />
                  </div>
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    {t('drop_video_here')}
                  </h4>
                  <p className="text-sm text-gray-500 mb-4">
                    {t('or_click_to_browse')}
                  </p>
                  <div className="text-xs text-gray-400">
                    <p>{t('supported_formats')}: MP4, AVI, MOV, WMV, FLV, WebM</p>
                    <p>{t('max_size')}: 100MB</p>
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="video/*"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                </div>
              ) : (
                <div className="h-full flex flex-col items-center justify-center space-y-6">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                    <Link className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="w-full max-w-md space-y-4">
                    <div>
                      <h4 className="text-lg font-medium text-gray-900 mb-2 text-center">
                        {t('enter_video_url')}
                      </h4>
                      <p className="text-sm text-gray-500 text-center mb-4">
                        {t('url_description')}
                      </p>
                    </div>
                    <div className="space-y-3">
                      <input
                        type="url"
                        placeholder={t('video_url_placeholder')}
                        value={urlInput}
                        onChange={(e) => setUrlInput(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleUrlSubmit()}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                      <button
                        onClick={handleUrlSubmit}
                        disabled={!urlInput}
                        className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
                      >
                        <Link className="h-4 w-4" />
                        {t('load_video')}
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-black">
      {/* Video Header */}
      <div className="bg-white px-4 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900 truncate">{video.name}</h3>
            <div className="flex items-center gap-4 mt-1">
              <Badge variant="outline">{video.type === 'file' ? t('local_file') : t('url')}</Badge>
              {duration > 0 && (
                <span className="text-sm text-gray-500">{formatTime(duration)}</span>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={startTranscription}
              disabled={isProcessing}
              size="sm"
            >
              {isProcessing ? t('processing') : t('generate_transcript')}
            </Button>
            <Button variant="outline" size="sm">
              <Share className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Video Player */}
      <div className="flex-1 relative">
        <video
          ref={videoRef}
          src={video.url}
          onTimeUpdate={handleTimeUpdate}
          onLoadedMetadata={handleLoadedMetadata}
          onEnded={() => setIsPlaying(false)}
          className="w-full h-full object-contain"
        />
        
        {/* Video Overlay Controls */}
        <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
          <Button
            variant="ghost"
            size="lg"
            onClick={handlePlayPause}
            className="text-white hover:bg-white hover:bg-opacity-20"
          >
            {isPlaying ? <Pause className="h-12 w-12" /> : <Play className="h-12 w-12" />}
          </Button>
        </div>
      </div>

      {/* Video Controls */}
      <div className="bg-gray-900 text-white p-4">
        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-sm">{formatTime(currentTime)}</span>
            <div className="flex-1">
              <Progress 
                value={(currentTime / duration) * 100 || 0} 
                className="cursor-pointer"
              />
            </div>
            <span className="text-sm">{formatTime(duration)}</span>
          </div>
        </div>

        {/* Control Buttons */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handlePlayPause}
              className="text-white hover:bg-white hover:bg-opacity-20"
            >
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleSeek(Math.max(0, currentTime - 10))}
              className="text-white hover:bg-white hover:bg-opacity-20"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleSeek(Math.min(duration, currentTime + 10))}
              className="text-white hover:bg-white hover:bg-opacity-20"
            >
              <FastForward className="h-4 w-4" />
            </Button>

            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleMute}
                className="text-white hover:bg-white hover:bg-opacity-20"
              >
                {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
              </Button>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={isMuted ? 0 : volume}
                onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                className="w-20"
              />
            </div>
          </div>

          <div className="flex items-center gap-2">
            <select
              value={playbackRate}
              onChange={(e) => handlePlaybackRateChange(parseFloat(e.target.value))}
              className="bg-transparent text-white text-sm border border-gray-600 rounded px-2 py-1"
            >
              <option value={0.5}>0.5x</option>
              <option value={0.75}>0.75x</option>
              <option value={1}>1x</option>
              <option value={1.25}>1.25x</option>
              <option value={1.5}>1.5x</option>
              <option value={2}>2x</option>
            </select>
            
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white hover:bg-opacity-20"
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
