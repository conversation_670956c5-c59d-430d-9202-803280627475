'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Upload,
  Link,
  Clock,
  Share,
  Download
} from 'lucide-react';
import { VideoData, TranscriptData } from '@/types/video';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

interface VideoPlayerProps {
  video: VideoData | null;
  onVideoSelect: (video: VideoData) => void;
  onTranscriptGenerated: (transcript: TranscriptData) => void;
  isProcessing: boolean;
  setIsProcessing: (processing: boolean) => void;
  transcript?: TranscriptData | null;
}

export default function VideoPlayer({
  video,
  onVideoSelect,
  onTranscriptGenerated,
  isProcessing,
  setIsProcessing,
  transcript
}: VideoPlayerProps) {
  const t = useTranslations('video');
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [inputMethod, setInputMethod] = useState<'file' | 'url'>('file');
  const [urlInput, setUrlInput] = useState('');
  const [videoError, setVideoError] = useState<string | null>(null);
  const [isVideoLoading, setIsVideoLoading] = useState(false);

  const videoRef = useRef<HTMLVideoElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (video && videoRef.current) {
      videoRef.current.load();
      setCurrentTime(0);
      setIsPlaying(false);
    }
  }, [video]);

  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
      setVideoError(null);
      setIsVideoLoading(false);
      console.log('Video metadata loaded:', {
        duration: videoRef.current.duration,
        videoWidth: videoRef.current.videoWidth,
        videoHeight: videoRef.current.videoHeight
      });
    }
  };

  const handleVideoError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    console.error('Video error:', e);
    setVideoError('Failed to load video. Please check the file format or URL.');
    setIsVideoLoading(false);
    toast.error('Failed to load video. Please check the file format or URL.');
  };

  const handleVideoLoadStart = () => {
    setIsVideoLoading(true);
    setVideoError(null);
    console.log('Video loading started');
  };

  const handleVolumeChange = (newVolume: number) => {
    if (videoRef.current) {
      videoRef.current.volume = newVolume;
      setVolume(newVolume);
      setIsMuted(newVolume === 0);
    }
  };

  const handleMute = () => {
    if (videoRef.current) {
      const newMuted = !isMuted;
      videoRef.current.muted = newMuted;
      setIsMuted(newMuted);
    }
  };

  const handleSeek = (time: number) => {
    if (videoRef.current) {
      videoRef.current.currentTime = time;
      setCurrentTime(time);
    }
  };

  const handlePlaybackRateChange = (rate: number) => {
    if (videoRef.current) {
      videoRef.current.playbackRate = rate;
      setPlaybackRate(rate);
    }
  };

  const formatTime = (time: number) => {
    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = Math.floor(time % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const validateVideoUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true; // Accept any valid URL
    } catch {
      return false;
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      // Check file type
      const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/webm'];
      if (!allowedTypes.includes(selectedFile.type)) {
        toast.error('Please select a valid video file (MP4, AVI, MOV, WMV, FLV, WebM)');
        return;
      }

      // Check file size (max 100MB)
      if (selectedFile.size > 100 * 1024 * 1024) {
        toast.error('File size must be less than 100MB');
        return;
      }

      const newVideo: VideoData = {
        id: Date.now().toString(),
        name: selectedFile.name,
        url: URL.createObjectURL(selectedFile),
        file: selectedFile,
        size: selectedFile.size,
        type: 'file',
        uploadedAt: new Date(),
      };

      onVideoSelect(newVideo);
      toast.success('Video uploaded successfully');
    }
  };

  const handleUrlSubmit = () => {
    if (!urlInput || !validateVideoUrl(urlInput)) {
      toast.error('Please enter a valid video URL');
      return;
    }

    const newVideo: VideoData = {
      id: Date.now().toString(),
      name: urlInput.split('/').pop() || 'Video from URL',
      url: urlInput,
      type: 'url',
      uploadedAt: new Date(),
    };

    onVideoSelect(newVideo);
    setUrlInput('');
    toast.success('Video URL added successfully');
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const droppedFile = event.dataTransfer.files[0];
    if (droppedFile && droppedFile.type.startsWith('video/')) {
      // Directly handle the dropped file instead of creating a fake event
      handleDroppedFile(droppedFile);
    }
  };

  const handleDroppedFile = (file: File) => {
    // Check file type
    const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/webm'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Please select a valid video file (MP4, AVI, MOV, WMV, FLV, WebM)');
      return;
    }

    // Check file size (max 100MB)
    if (file.size > 100 * 1024 * 1024) {
      toast.error('File size must be less than 100MB');
      return;
    }

    const newVideo: VideoData = {
      id: Date.now().toString(),
      name: file.name,
      url: URL.createObjectURL(file),
      file: file,
      size: file.size,
      type: 'file',
      uploadedAt: new Date(),
    };

    onVideoSelect(newVideo);
    toast.success('Video uploaded successfully');
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const startTranscription = async () => {
    if (!video) return;

    setIsProcessing(true);
    toast.info(t('starting_transcription'));

    try {
      const formData = new FormData();

      if (video.type === 'file' && video.file) {
        formData.append('video', video.file);
      } else {
        formData.append('video', video.url);
      }

      formData.append('task', 'transcribe');
      formData.append('language', 'None'); // Auto-detect
      formData.append('batch_size', '64');
      formData.append('return_timestamps', 'true');

      const response = await fetch('/api/video', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || errorData.error || 'Transcription failed');
      }

      const result = await response.json();

      const transcriptData: TranscriptData = {
        id: Date.now().toString(),
        videoId: video.id,
        segments: result.segments || [],
        fullText: result.text || '',
        language: result.language || 'auto-detected',
        generatedAt: new Date()
      };

      onTranscriptGenerated(transcriptData);
      toast.success(t('transcription_completed'));
    } catch (error: any) {
      console.error('Transcription error:', error);
      toast.error(error.message || t('transcription_failed'));
    } finally {
      setIsProcessing(false);
    }
  };

  if (!video) {
    return (
      <div className="h-full flex flex-col bg-white">
        {/* Upload Header */}
        <div className="bg-white px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">{t('upload_video')}</h3>
          <p className="text-sm text-gray-500 mt-1">{t('upload_video_description')}</p>
        </div>

        {/* Upload Area */}
        <div className="flex-1 p-6">
          <div className="h-full flex flex-col">
            {/* Upload Tabs */}
            <div className="mb-6">
              <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                <button
                  onClick={() => setInputMethod('file')}
                  className={`flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    inputMethod === 'file'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Upload className="h-4 w-4" />
                  {t('upload_file')}
                </button>
                <button
                  onClick={() => setInputMethod('url')}
                  className={`flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    inputMethod === 'url'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Link className="h-4 w-4" />
                  {t('from_url')}
                </button>
              </div>
            </div>

            {/* Upload Content */}
            <div className="flex-1">
              {inputMethod === 'file' ? (
                <div
                  className="h-full border-2 border-dashed border-gray-300 rounded-xl p-8 text-center cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 flex flex-col items-center justify-center"
                  onDrop={handleDrop}
                  onDragOver={handleDragOver}
                  onClick={() => fileInputRef.current?.click()}
                >
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <Upload className="h-8 w-8 text-blue-600" />
                  </div>
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    {t('drop_video_here')}
                  </h4>
                  <p className="text-sm text-gray-500 mb-4">
                    {t('or_click_to_browse')}
                  </p>
                  <div className="text-xs text-gray-400">
                    <p>{t('supported_formats')}: MP4, AVI, MOV, WMV, FLV, WebM</p>
                    <p>{t('max_size')}: 100MB</p>
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="video/*"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                </div>
              ) : (
                <div className="h-full flex flex-col items-center justify-center space-y-6">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                    <Link className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="w-full max-w-md space-y-4">
                    <div>
                      <h4 className="text-lg font-medium text-gray-900 mb-2 text-center">
                        {t('enter_video_url')}
                      </h4>
                      <p className="text-sm text-gray-500 text-center mb-4">
                        {t('url_description')}
                      </p>
                    </div>
                    <div className="space-y-3">
                      <input
                        type="url"
                        placeholder={t('video_url_placeholder')}
                        value={urlInput}
                        onChange={(e) => setUrlInput(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && handleUrlSubmit()}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                      <button
                        onClick={handleUrlSubmit}
                        disabled={!urlInput}
                        className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
                      >
                        <Link className="h-4 w-4" />
                        {t('load_video')}
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Video Header */}
      <div className="bg-white px-4 py-3 border-b border-gray-200 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <h3 className="text-base font-semibold text-gray-900 truncate">{video.name}</h3>
            <div className="flex items-center gap-3 mt-1">
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs">
                {video.type === 'file' ? t('local_file') : t('url')}
              </Badge>
              {duration > 0 && (
                <span className="text-xs text-gray-500 flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {formatTime(duration)}
                </span>
              )}
              {video.size && (
                <span className="text-xs text-gray-500">
                  {(video.size / 1024 / 1024).toFixed(1)} MB
                </span>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={startTranscription}
              disabled={isProcessing}
              size="sm"
              className="bg-blue-600 hover:bg-blue-700 text-xs"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                  {t('processing')}
                </>
              ) : (
                t('generate_transcript')
              )}
            </Button>
            <Button variant="outline" size="sm" className="h-8 w-8 p-0">
              <Share className="h-3 w-3" />
            </Button>
            <Button variant="outline" size="sm" className="h-8 w-8 p-0">
              <Download className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>

      {/* Simple Video Player */}
      <div className="bg-black relative flex-shrink-0" style={{ minHeight: '400px' }}>
        <video
          ref={videoRef}
          src={video.url}
          onTimeUpdate={handleTimeUpdate}
          onLoadedMetadata={handleLoadedMetadata}
          onError={handleVideoError}
          onLoadStart={handleVideoLoadStart}
          onCanPlay={() => {
            setIsVideoLoading(false);
            console.log('Video can play');
          }}
          className="w-full h-full rounded-lg"
          controls
          preload="metadata"
          playsInline
          crossOrigin="anonymous"
        />

        {/* Loading Overlay */}
        {(isProcessing || isVideoLoading) && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
            <div className="text-white text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
              <p className="text-sm">
                {isProcessing ? t('processing_video') : 'Loading video...'}
              </p>
            </div>
          </div>
        )}

        {/* Error Overlay */}
        {videoError && (
          <div className="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center rounded-lg">
            <div className="text-white text-center p-6">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <p className="text-sm mb-4">{videoError}</p>
              <Button
                onClick={() => {
                  setVideoError(null);
                  if (videoRef.current) {
                    videoRef.current.load();
                  }
                }}
                variant="outline"
                size="sm"
                className="text-white border-white hover:bg-white hover:text-black"
              >
                Retry
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Timeline and Transcript */}
      {transcript && (
        <div className="flex-1 bg-white border-t border-gray-200 overflow-hidden min-h-0">
          <div className="h-full flex flex-col">
            {/* Timeline Header */}
            <div className="px-4 py-2 border-b border-gray-200 bg-gray-50 flex-shrink-0">
              <h4 className="text-sm font-semibold text-gray-900 flex items-center gap-2">
                <Clock className="h-4 w-4" />
                {t('timeline')}
              </h4>
              <p className="text-xs text-gray-500 mt-0.5">{t('timeline_description')}</p>
            </div>

            {/* Timeline Content */}
            <div className="flex-1 overflow-y-auto p-3">
              <div className="space-y-1.5">
                {transcript.segments.map((segment) => (
                  <div
                    key={segment.id}
                    className={`p-2.5 rounded-md border cursor-pointer transition-all duration-200 hover:bg-blue-50 hover:border-blue-200 ${
                      currentTime >= segment.start && currentTime <= segment.end
                        ? 'bg-blue-100 border-blue-300 shadow-sm'
                        : 'bg-white border-gray-200'
                    }`}
                    onClick={() => handleSeek(segment.start)}
                  >
                    <div className="flex items-start gap-2.5">
                      <div className="flex-shrink-0">
                        <Badge
                          variant="outline"
                          className={`text-xs px-2 py-0.5 ${
                            currentTime >= segment.start && currentTime <= segment.end
                              ? 'bg-blue-200 text-blue-800 border-blue-300'
                              : 'bg-gray-100 text-gray-600'
                          }`}
                        >
                          {formatTime(segment.start)}
                        </Badge>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-900 leading-relaxed">
                          {segment.text}
                        </p>
                        {segment.confidence && (
                          <div className="mt-1">
                            <span className="text-xs text-gray-500">
                              {t('confidence')}: {(segment.confidence * 100).toFixed(1)}%
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
