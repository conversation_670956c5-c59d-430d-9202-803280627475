'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  Maximize, 
  Settings,
  Download,
  Share,
  RotateCcw,
  FastForward
} from 'lucide-react';
import { VideoData, TranscriptData } from '@/types/video';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

interface VideoPlayerProps {
  video: VideoData | null;
  onTranscriptGenerated: (transcript: TranscriptData) => void;
  isProcessing: boolean;
  setIsProcessing: (processing: boolean) => void;
}

export default function VideoPlayer({ 
  video, 
  onTranscriptGenerated, 
  isProcessing, 
  setIsProcessing 
}: VideoPlayerProps) {
  const t = useTranslations('video');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [playbackRate, setPlaybackRate] = useState(1);
  
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (video && videoRef.current) {
      videoRef.current.load();
      setCurrentTime(0);
      setIsPlaying(false);
    }
  }, [video]);

  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };

  const handleVolumeChange = (newVolume: number) => {
    if (videoRef.current) {
      videoRef.current.volume = newVolume;
      setVolume(newVolume);
      setIsMuted(newVolume === 0);
    }
  };

  const handleMute = () => {
    if (videoRef.current) {
      const newMuted = !isMuted;
      videoRef.current.muted = newMuted;
      setIsMuted(newMuted);
    }
  };

  const handleSeek = (time: number) => {
    if (videoRef.current) {
      videoRef.current.currentTime = time;
      setCurrentTime(time);
    }
  };

  const handlePlaybackRateChange = (rate: number) => {
    if (videoRef.current) {
      videoRef.current.playbackRate = rate;
      setPlaybackRate(rate);
    }
  };

  const formatTime = (time: number) => {
    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = Math.floor(time % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const startTranscription = async () => {
    if (!video) return;
    
    setIsProcessing(true);
    toast.info(t('starting_transcription'));
    
    try {
      // TODO: 这里将来会调用实际的转录API
      // 模拟转录过程
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const mockTranscript: TranscriptData = {
        id: Date.now().toString(),
        videoId: video.id,
        segments: [
          {
            id: '1',
            start: 0,
            end: 5,
            text: 'Welcome to this video demonstration.',
            confidence: 0.95
          },
          {
            id: '2',
            start: 5,
            end: 12,
            text: 'In this video, we will explore various topics and concepts.',
            confidence: 0.92
          },
          {
            id: '3',
            start: 12,
            end: 18,
            text: 'Please follow along as we dive into the details.',
            confidence: 0.88
          }
        ],
        fullText: 'Welcome to this video demonstration. In this video, we will explore various topics and concepts. Please follow along as we dive into the details.',
        language: 'english',
        generatedAt: new Date()
      };
      
      onTranscriptGenerated(mockTranscript);
      toast.success(t('transcription_completed'));
    } catch (error) {
      toast.error(t('transcription_failed'));
      console.error('Transcription error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  if (!video) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <Play className="mx-auto h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">{t('no_video_selected')}</h3>
          <p className="text-sm text-gray-500">{t('select_video_to_start')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-black">
      {/* Video Header */}
      <div className="bg-white px-4 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900 truncate">{video.name}</h3>
            <div className="flex items-center gap-4 mt-1">
              <Badge variant="outline">{video.type === 'file' ? t('local_file') : t('url')}</Badge>
              {duration > 0 && (
                <span className="text-sm text-gray-500">{formatTime(duration)}</span>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={startTranscription}
              disabled={isProcessing}
              size="sm"
            >
              {isProcessing ? t('processing') : t('generate_transcript')}
            </Button>
            <Button variant="outline" size="sm">
              <Share className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Video Player */}
      <div className="flex-1 relative">
        <video
          ref={videoRef}
          src={video.url}
          onTimeUpdate={handleTimeUpdate}
          onLoadedMetadata={handleLoadedMetadata}
          onEnded={() => setIsPlaying(false)}
          className="w-full h-full object-contain"
        />
        
        {/* Video Overlay Controls */}
        <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
          <Button
            variant="ghost"
            size="lg"
            onClick={handlePlayPause}
            className="text-white hover:bg-white hover:bg-opacity-20"
          >
            {isPlaying ? <Pause className="h-12 w-12" /> : <Play className="h-12 w-12" />}
          </Button>
        </div>
      </div>

      {/* Video Controls */}
      <div className="bg-gray-900 text-white p-4">
        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-sm">{formatTime(currentTime)}</span>
            <div className="flex-1">
              <Progress 
                value={(currentTime / duration) * 100 || 0} 
                className="cursor-pointer"
              />
            </div>
            <span className="text-sm">{formatTime(duration)}</span>
          </div>
        </div>

        {/* Control Buttons */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handlePlayPause}
              className="text-white hover:bg-white hover:bg-opacity-20"
            >
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleSeek(Math.max(0, currentTime - 10))}
              className="text-white hover:bg-white hover:bg-opacity-20"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleSeek(Math.min(duration, currentTime + 10))}
              className="text-white hover:bg-white hover:bg-opacity-20"
            >
              <FastForward className="h-4 w-4" />
            </Button>

            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleMute}
                className="text-white hover:bg-white hover:bg-opacity-20"
              >
                {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
              </Button>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={isMuted ? 0 : volume}
                onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                className="w-20"
              />
            </div>
          </div>

          <div className="flex items-center gap-2">
            <select
              value={playbackRate}
              onChange={(e) => handlePlaybackRateChange(parseFloat(e.target.value))}
              className="bg-transparent text-white text-sm border border-gray-600 rounded px-2 py-1"
            >
              <option value={0.5}>0.5x</option>
              <option value={0.75}>0.75x</option>
              <option value={1}>1x</option>
              <option value={1.25}>1.25x</option>
              <option value={1.5}>1.5x</option>
              <option value={2}>2x</option>
            </select>
            
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white hover:bg-opacity-20"
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
