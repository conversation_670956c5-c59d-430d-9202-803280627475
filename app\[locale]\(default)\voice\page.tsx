import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import VoicePageClient from '@/components/voice/voice-page-client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Mic, FileAudio, Clock, Download } from 'lucide-react';

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('voice');

  return {
    title: `${t('title')} - AI Speech to Text`,
    description: t('description'),
    keywords: 'voice transcription, speech to text, audio to text, AI transcription, whisper',
  };
}

export default async function VoicePage() {
  const t = await getTranslations('voice');

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          {t('title')}
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          {t('description')}
        </p>
      </div>

      {/* Features */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="text-center">
            <Mic className="h-8 w-8 mx-auto text-blue-500 mb-2" />
            <CardTitle className="text-lg">{t('high_accuracy')}</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription className="text-center">
              {t('high_accuracy_desc')}
            </CardDescription>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <FileAudio className="h-8 w-8 mx-auto text-green-500 mb-2" />
            <CardTitle className="text-lg">{t('multiple_formats')}</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription className="text-center">
              {t('multiple_formats_desc')}
            </CardDescription>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <Clock className="h-8 w-8 mx-auto text-purple-500 mb-2" />
            <CardTitle className="text-lg">{t('timeline_view')}</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription className="text-center">
              {t('timeline_view_desc')}
            </CardDescription>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <Download className="h-8 w-8 mx-auto text-orange-500 mb-2" />
            <CardTitle className="text-lg">{t('easy_export')}</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription className="text-center">
              {t('easy_export_desc')}
            </CardDescription>
          </CardContent>
        </Card>
      </div>

      {/* Main Transcription Component */}
      <VoicePageClient />

      {/* Usage Instructions */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>{t('how_to_use')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-xl font-bold text-blue-600">1</span>
              </div>
              <h3 className="font-semibold mb-2">{t('step_1_title')}</h3>
              <p className="text-sm text-gray-600">
                {t('step_1_description')}
              </p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-xl font-bold text-green-600">2</span>
              </div>
              <h3 className="font-semibold mb-2">{t('step_2_title')}</h3>
              <p className="text-sm text-gray-600">
                {t('step_2_description')}
              </p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-xl font-bold text-purple-600">3</span>
              </div>
              <h3 className="font-semibold mb-2">{t('step_3_title')}</h3>
              <p className="text-sm text-gray-600">
                {t('step_3_description')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Technical Details */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>{t('technical_details')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">{t('supported_formats')}</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• MP3 (MPEG Audio Layer 3)</li>
                <li>• WAV (Waveform Audio File Format)</li>
                <li>• M4A (MPEG-4 Audio)</li>
                <li>• OGG (Ogg Vorbis)</li>
                <li>• FLAC (Free Lossless Audio Codec)</li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-2">{t('features')}</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• {t('max_file_size')}</li>
                <li>• {t('auto_language')}</li>
                <li>• {t('timestamp_generation')}</li>
                <li>• {t('confidence_scoring')}</li>
                <li>• {t('realtime_status')}</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
