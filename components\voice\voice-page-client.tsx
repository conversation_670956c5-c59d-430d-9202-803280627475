'use client';

import VoiceTranscription from './voice-transcription';
import { VoiceTranscriptionStatus } from '@/types/voice';

interface VoicePageClientProps {
  // Add any props you need from the server component
}

export default function VoicePageClient({}: VoicePageClientProps) {
  const handleTranscriptionComplete = (result: VoiceTranscriptionStatus) => {
    console.log('Transcription completed:', result);
    // Add any additional client-side logic here
  };

  return (
    <VoiceTranscription 
      onTranscriptionComplete={handleTranscriptionComplete}
    />
  );
}
