'use client';

import { useRef } from 'react';
import { Upload, FileVideo } from 'lucide-react';
import { VideoData } from '@/types/video.d';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

interface VideoFileUploadProps {
  onVideoSelect: (video: VideoData) => void;
}

export default function VideoFileUpload({ onVideoSelect }: VideoFileUploadProps) {
  const t = useTranslations('video');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 支持的视频格式
  const SUPPORTED_FORMATS = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/webm', 'video/mkv', 'video/m4v'];
  const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB

  // 验证文件
  const validateFile = (file: File): boolean => {
    // 检查文件类型
    if (!SUPPORTED_FORMATS.includes(file.type)) {
      toast.error(`Unsupported file format. Please select: ${SUPPORTED_FORMATS.map(f => f.split('/')[1].toUpperCase()).join(', ')}`);
      return false;
    }

    // 检查文件大小
    if (file.size > MAX_FILE_SIZE) {
      toast.error(`File size must be less than ${MAX_FILE_SIZE / 1024 / 1024}MB`);
      return false;
    }

    return true;
  };

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      processFile(selectedFile);
    }
  };

  // 处理拖拽上传
  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const droppedFile = event.dataTransfer.files[0];
    if (droppedFile && droppedFile.type.startsWith('video/')) {
      processFile(droppedFile);
    } else {
      toast.error('Please drop a video file');
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  // 处理文件
  const processFile = (file: File) => {
    if (!validateFile(file)) {
      return;
    }

    // 创建视频对象
    const newVideo: VideoData = {
      id: Date.now().toString(),
      name: file.name,
      url: URL.createObjectURL(file),
      file: file,
      size: file.size,
      type: 'file',
      uploadedAt: new Date(),
    };

    onVideoSelect(newVideo);
    toast.success(`Video "${file.name}" uploaded successfully`);
    
    // 清空文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="h-full flex flex-col items-center justify-center p-6">
      {/* 上传区域 */}
      <div
        className="w-full max-w-md h-64 border-2 border-dashed border-gray-300 rounded-xl p-6 text-center cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 flex flex-col items-center justify-center"
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onClick={() => fileInputRef.current?.click()}
      >
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
          <Upload className="h-8 w-8 text-blue-600" />
        </div>
        
        <h4 className="text-lg font-medium text-gray-900 mb-2">
          {t('drop_video_here')}
        </h4>
        
        <p className="text-sm text-gray-500 mb-4">
          {t('or_click_to_browse')}
        </p>
        
        <div className="text-xs text-gray-400 space-y-1">
          <p>
            <strong>Supported formats:</strong> MP4, AVI, MOV, WMV, FLV, WebM, MKV, M4V
          </p>
          <p>
            <strong>Maximum size:</strong> {formatFileSize(MAX_FILE_SIZE)}
          </p>
        </div>
        
        <input
          ref={fileInputRef}
          type="file"
          accept="video/*"
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>
      
      {/* 格式说明 */}
      <div className="mt-6 w-full max-w-md">
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-3">
            <FileVideo className="h-5 w-5 text-gray-600" />
            <span className="text-sm font-medium text-gray-700">
              Video File Upload
            </span>
          </div>
          
          <div className="space-y-2 text-xs text-gray-600">
            <div>
              <strong>Best formats:</strong> MP4 (H.264), WebM
            </div>
            <div>
              <strong>Quality tips:</strong> 1080p or lower for best performance
            </div>
            <div>
              <strong>Processing:</strong> Files are processed locally for privacy
            </div>
          </div>
        </div>
      </div>
      
      {/* 快速操作提示 */}
      <div className="mt-4 text-center">
        <p className="text-xs text-gray-500">
          💡 Tip: You can also drag and drop video files directly onto this area
        </p>
      </div>
    </div>
  );
}
