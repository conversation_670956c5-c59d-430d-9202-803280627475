'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Copy, 
  Download, 
  Search, 
  Edit3, 
  Save, 
  Clock,
  FileText,
  Eye,
  EyeOff
} from 'lucide-react';
import { VideoData, TranscriptData, TranscriptSegment } from '@/types/video';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

interface VideoTranscriptProps {
  transcript: TranscriptData | null;
  video: VideoData | null;
}

export default function VideoTranscript({ transcript, video }: VideoTranscriptProps) {
  const t = useTranslations('video');
  const [isEditing, setIsEditing] = useState(false);
  const [editedText, setEditedText] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [showTimestamps, setShowTimestamps] = useState(true);
  const [activeTab, setActiveTab] = useState('segments');

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleCopyTranscript = () => {
    if (transcript) {
      navigator.clipboard.writeText(transcript.fullText);
      toast.success(t('transcript_copied'));
    }
  };

  const handleDownloadTranscript = () => {
    if (transcript) {
      const blob = new Blob([transcript.fullText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `transcript-${video?.name || 'video'}-${Date.now()}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success(t('transcript_downloaded'));
    }
  };

  const handleEditStart = () => {
    if (transcript) {
      setEditedText(transcript.fullText);
      setIsEditing(true);
    }
  };

  const handleEditSave = () => {
    // TODO: 这里将来会保存编辑后的转录文本
    setIsEditing(false);
    toast.success(t('transcript_saved'));
  };

  const handleEditCancel = () => {
    setIsEditing(false);
    setEditedText('');
  };

  const filteredSegments = transcript?.segments.filter(segment =>
    segment.text.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const highlightSearchTerm = (text: string) => {
    if (!searchTerm) return text;
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<mark class="bg-yellow-200">$1</mark>');
  };

  if (!transcript) {
    return (
      <div className="h-full bg-white">
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">{t('transcript')}</h3>
          <p className="text-sm text-gray-500 mt-1">{t('transcript_description')}</p>
        </div>
        
        <div className="h-full flex items-center justify-center">
          <div className="text-center">
            <FileText className="mx-auto h-16 w-16 text-gray-400 mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">{t('no_transcript_yet')}</h4>
            <p className="text-sm text-gray-500 max-w-sm">
              {t('generate_transcript_instruction')}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-white flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <div>
            <h3 className="text-lg font-medium text-gray-900">{t('transcript')}</h3>
            <div className="flex items-center gap-4 mt-1">
              <Badge variant="outline">
                {transcript.language || t('auto_detected')}
              </Badge>
              <span className="text-sm text-gray-500">
                {transcript.segments.length} {t('segments')}
              </span>
              <span className="text-sm text-gray-500">
                {transcript.generatedAt.toLocaleDateString()}
              </span>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowTimestamps(!showTimestamps)}
            >
              {showTimestamps ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {showTimestamps ? t('hide_timestamps') : t('show_timestamps')}
            </Button>
            
            {!isEditing ? (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleEditStart}
                >
                  <Edit3 className="h-4 w-4 mr-2" />
                  {t('edit')}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyTranscript}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  {t('copy')}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDownloadTranscript}
                >
                  <Download className="h-4 w-4 mr-2" />
                  {t('download')}
                </Button>
              </>
            ) : (
              <>
                <Button
                  size="sm"
                  onClick={handleEditSave}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {t('save')}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleEditCancel}
                >
                  {t('cancel')}
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Search */}
        {!isEditing && (
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder={t('search_transcript')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {isEditing ? (
          <div className="h-full p-4">
            <Textarea
              value={editedText}
              onChange={(e) => setEditedText(e.target.value)}
              className="h-full resize-none font-mono text-sm"
              placeholder={t('edit_transcript_placeholder')}
            />
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="mx-4 mt-4">
              <TabsTrigger value="segments" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                {t('segments')}
              </TabsTrigger>
              <TabsTrigger value="fulltext" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                {t('full_text')}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="segments" className="flex-1 overflow-y-auto p-4 pt-2">
              <div className="space-y-3">
                {(searchTerm ? filteredSegments : transcript.segments).map((segment) => (
                  <Card 
                    key={segment.id}
                    className="hover:bg-gray-50 cursor-pointer transition-colors"
                    onClick={() => {
                      // TODO: 跳转到视频的对应时间
                      console.log('Jump to time:', segment.start);
                    }}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        {showTimestamps && (
                          <Badge variant="outline" className="shrink-0">
                            {formatTime(segment.start)} - {formatTime(segment.end)}
                          </Badge>
                        )}
                        <div className="flex-1">
                          <p 
                            className="text-sm leading-relaxed"
                            dangerouslySetInnerHTML={{ 
                              __html: highlightSearchTerm(segment.text) 
                            }}
                          />
                          {segment.confidence && (
                            <div className="mt-2">
                              <span className="text-xs text-gray-500">
                                {t('confidence')}: {(segment.confidence * 100).toFixed(1)}%
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="fulltext" className="flex-1 overflow-y-auto p-4 pt-2">
              <Card>
                <CardContent className="p-6">
                  <div 
                    className="prose prose-sm max-w-none leading-relaxed"
                    dangerouslySetInnerHTML={{ 
                      __html: highlightSearchTerm(transcript.fullText) 
                    }}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}
      </div>

      {/* Footer Stats */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center gap-4">
            <span>{transcript.fullText.length} {t('characters')}</span>
            <span>{transcript.fullText.split(' ').length} {t('words')}</span>
            <span>{transcript.segments.length} {t('segments')}</span>
          </div>
          {searchTerm && (
            <span>
              {filteredSegments.length} {t('search_results')}
            </span>
          )}
        </div>
      </div>
    </div>
  );
}
