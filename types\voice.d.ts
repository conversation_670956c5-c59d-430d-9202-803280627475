export interface VoiceTranscriptionRequest {
  audio: string | File | Blob;
  batch_size?: number;
  timestamp?: 'chunk' | 'word' | boolean;
  diarize_audio?: boolean;
}

export interface VoiceTranscriptionResponse {
  text: string;
  segments?: TranscriptionSegment[];
  language?: string;
}

export interface TranscriptionSegment {
  id: number;
  seek: number;
  start: number;
  end: number;
  text: string;
  tokens: number[];
  temperature: number;
  avg_logprob: number;
  compression_ratio: number;
  no_speech_prob: number;
}

export interface VoiceTranscriptionStatus {
  id: string;
  status: 'starting' | 'processing' | 'succeeded' | 'failed' | 'canceled';
  input?: VoiceTranscriptionRequest;
  output?: VoiceTranscriptionResponse;
  error?: string;
  logs?: string;
  created_at?: string;
  started_at?: string;
  completed_at?: string;
}

export interface VoiceUploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}
