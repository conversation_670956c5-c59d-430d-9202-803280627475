export interface VoiceTranscriptionRequest {
  audio: string | File | Blob;
  task?: 'transcribe' | 'translate';
  batch_size?: number;
  return_timestamps?: boolean;
  language?: string;
}

export interface VoiceTranscriptionResponse {
  text: string;
  chunks?: TranscriptionChunk[];
  language?: string;
}

export interface TranscriptionChunk {
  text: string;
  timestamp: [number, number]; // [start, end] in seconds
}

// Keep the old interface for backward compatibility
export interface TranscriptionSegment {
  id: number;
  seek: number;
  start: number;
  end: number;
  text: string;
  tokens: number[];
  temperature: number;
  avg_logprob: number;
  compression_ratio: number;
  no_speech_prob: number;
}

export interface VoiceTranscriptionStatus {
  id: string;
  status: 'starting' | 'processing' | 'succeeded' | 'failed' | 'canceled';
  input?: VoiceTranscriptionRequest;
  output?: VoiceTranscriptionResponse;
  error?: string;
  logs?: string;
  created_at?: string;
  started_at?: string;
  completed_at?: string;
}

export interface VoiceUploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}
