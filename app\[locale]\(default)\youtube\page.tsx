"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { Loader2, Play, Pause } from "lucide-react"
import { toast } from "sonner"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"

export default function YouTubePage() {
  const [url, setUrl] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [activeTab, setActiveTab] = useState("formatted")
  const [currentMedia, setCurrentMedia] = useState<any>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const mediaRef = useRef<HTMLAudioElement | HTMLVideoElement | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const response = await fetch("/api/youtube", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ url }),
      })

      if (!response.ok) {
        throw new Error("Failed to process video")
      }

      const data = await response.json()
      setResult(data)
      toast.success("Video processed successfully")
    } catch (error) {
      console.error("Error:", error)
      toast.error("Failed to process video")
    } finally {
      setIsLoading(false)
    }
  }

  const handlePlayMedia = (format: any) => {
    // Stop current media if playing
    if (mediaRef.current) {
      mediaRef.current.pause()
      mediaRef.current = null
    }

    setCurrentMedia(format)
    setIsPlaying(true)
    
    if (format.type === 'audio') {
      const audio = new Audio(format.url)
      audio.addEventListener('timeupdate', () => {
        setCurrentTime(audio.currentTime)
        setDuration(audio.duration)
      })
      audio.addEventListener('ended', () => {
        setIsPlaying(false)
        setCurrentTime(0)
      })
      audio.play()
      mediaRef.current = audio
    } else if (format.type === 'video') {
      const video = document.createElement('video')
      video.src = format.url
      video.controls = true
      video.addEventListener('timeupdate', () => {
        setCurrentTime(video.currentTime)
        setDuration(video.duration)
      })
      video.addEventListener('ended', () => {
        setIsPlaying(false)
        setCurrentTime(0)
      })
      video.play()
      mediaRef.current = video
    }
  }

  const handlePause = () => {
    if (mediaRef.current) {
      mediaRef.current.pause()
      setIsPlaying(false)
    }
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${String(seconds).padStart(2, '0')}`
  }

  return (
    <main className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">YouTube Video Processor</h1>
      
      <Card className="p-6 mb-6">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="url" className="text-sm font-medium">
              YouTube Video URL
            </label>
            <Input
              id="url"
              type="url"
              placeholder="Enter YouTube video URL"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              required
            />
          </div>
          
          <Button type="submit" disabled={isLoading} className="w-full">
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              "Process Video"
            )}
          </Button>
        </form>
      </Card>

      {result && (
        <Card className="p-6 bg-gray-900 border-gray-800">
          <Tabs defaultValue="formatted" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="formatted">Formatted View</TabsTrigger>
              <TabsTrigger value="json">JSON View</TabsTrigger>
            </TabsList>
            
            <TabsContent value="formatted">
              <div className="mt-4">
                {/* Video Info */}
                <div className="mb-6">
                  <div className="flex items-center gap-4 mb-4">
                    <img 
                      src={result.thumbnail} 
                      alt={result.title} 
                      className="w-32 h-18 object-cover rounded-lg"
                    />
                    <div>
                      <h3 className="text-lg font-medium text-white">{result.title}</h3>
                      <p className="text-gray-400">Duration: {Math.floor(result.duration / 60)}:{String(result.duration % 60).padStart(2, '0')}</p>
                    </div>
                  </div>
                </div>

                {/* Current Player */}
                {currentMedia && (
                  <div className="mb-6 bg-gray-800 p-4 rounded-lg border border-gray-700">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-white font-medium">{currentMedia.label}</h4>
                      <div className="flex items-center gap-2">
                        <span className="text-gray-400">{formatTime(currentTime)} / {formatTime(duration)}</span>
                        {isPlaying ? (
                          <Button variant="outline" size="sm" onClick={handlePause}>
                            <Pause className="h-4 w-4" />
                          </Button>
                        ) : (
                          <Button variant="outline" size="sm" onClick={() => handlePlayMedia(currentMedia)}>
                            <Play className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                    {currentMedia.type === 'video' && (
                      <video 
                        src={currentMedia.url} 
                        controls 
                        className="w-full rounded-lg"
                      />
                    )}
                  </div>
                )}

                {/* Video Formats */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-white">Available Formats</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {result.medias.map((format: any, index: number) => (
                      <div 
                        key={index}
                        className="bg-gray-800 p-4 rounded-lg border border-gray-700"
                      >
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-white font-medium">{format.label}</span>
                          {format.type === 'video' && (
                            <span className="text-gray-400 text-sm">
                              {format.width}x{format.height}
                              {format.fps && ` @ ${format.fps}fps`}
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-400">
                          <p>Type: {format.type}</p>
                          <p>Format: {format.ext}</p>
                          {format.bitrate && (
                            <p>Bitrate: {(format.bitrate / 1000).toFixed(2)} kbps</p>
                          )}
                        </div>
                        <Button 
                          variant="outline" 
                          className="mt-2 w-full"
                          onClick={() => handlePlayMedia(format)}
                        >
                          Play {format.type === 'audio' ? 'Audio' : 'Video'}
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="json">
              <pre className="bg-gray-800 text-gray-100 p-4 rounded-md overflow-auto border border-gray-700 mt-4">
                {JSON.stringify(result, null, 2)}
              </pre>
            </TabsContent>
          </Tabs>
        </Card>
      )}
    </main>
  )
} 