'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Send,
  Bot,
  Trash2,
  Copy,
  ThumbsUp,
  ThumbsDown,
  Sparkles
} from 'lucide-react';
import { VideoData, TranscriptData, ChatMessage } from '@/types/video.d';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';
import ModelSelector from './model-selector';

interface VideoChatContentProps {
  messages: ChatMessage[];
  onSendMessage: (message: string, model?: string) => void;
  transcript: TranscriptData | null;
  video: VideoData | null;
}

export default function VideoChatContent({ messages, onSendMessage, transcript, video }: VideoChatContentProps) {
  const t = useTranslations('video');
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [selectedModel, setSelectedModel] = useState('openai/gpt-4o-mini');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = () => {
    if (inputMessage.trim()) {
      onSendMessage(inputMessage.trim(), selectedModel);
      setInputMessage('');
      setIsTyping(true);

      // AI回复后停止typing状态
      setTimeout(() => {
        setIsTyping(false);
      }, 3000);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleCopyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
    toast.success(t('message_copied'));
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const suggestedQuestions = [
    t('what_is_main_topic'),
    t('summarize_video'),
    t('key_points'),
    t('explain_concept'),
  ];

  return (
    <div className="h-full bg-gray-50 flex flex-col">
      {/* Simplified Header */}
      <div className="p-4 border-b border-gray-200 bg-white flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
              <Bot className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{t('ai_assistant')}</h3>
              <div className="flex items-center gap-2 mt-1">
                <Badge
                  variant={video ? 'default' : 'secondary'}
                  className={`text-xs ${video ? 'bg-green-100 text-green-800 border-green-200' : ''}`}
                >
                  {video ? t('video_loaded') : t('no_video')}
                </Badge>
                <Badge
                  variant={transcript ? 'default' : 'secondary'}
                  className={`text-xs ${transcript ? 'bg-blue-100 text-blue-800 border-blue-200' : ''}`}
                >
                  {transcript ? t('transcript_ready') : t('no_transcript')}
                </Badge>
              </div>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            disabled={messages.length === 0}
            className="text-gray-400 hover:text-red-500 hover:bg-red-50"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-6 space-y-6 min-h-0">
        {messages.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <Sparkles className="h-10 w-10 text-white" />
            </div>
            <h4 className="text-xl font-semibold text-gray-900 mb-3">{t('start_conversation')}</h4>
            <p className="text-gray-600 mb-8 max-w-sm mx-auto leading-relaxed">{t('ai_help_description')}</p>

            {/* Suggested Questions */}
            <div className="space-y-4">
              <p className="text-sm font-semibold text-gray-700 uppercase tracking-wide">{t('suggested_questions')}</p>
              <div className="grid gap-3 max-w-md mx-auto">
                {suggestedQuestions.map((question, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="default"
                    onClick={() => setInputMessage(question)}
                    className="text-left justify-start p-4 h-auto hover:bg-blue-50 hover:border-blue-200 transition-all duration-200"
                    disabled={!transcript}
                  >
                    <span className="text-sm leading-relaxed">{question}</span>
                  </Button>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                {message.sender === 'ai' && (
                  <div className="flex-shrink-0 mr-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                      <Bot className="h-4 w-4 text-white" />
                    </div>
                  </div>
                )}

                <div className={`max-w-[75%] ${message.sender === 'user' ? '' : 'flex-1'}`}>
                  {message.sender === 'user' ? (
                    /* 简化的用户消息 */
                    <div className="bg-blue-500 text-white rounded-2xl rounded-br-md px-4 py-3 shadow-sm">
                      <p className="text-sm leading-relaxed">{message.content}</p>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs text-blue-100">{formatTime(message.timestamp)}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCopyMessage(message.content)}
                          className="h-5 w-5 p-0 text-blue-100 hover:text-white hover:bg-blue-600 rounded-full"
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ) : (
                    /* AI消息保持卡片样式 */
                    <Card className="bg-white border border-gray-200 shadow-sm">
                      <CardContent className="p-4">
                        <p className="text-sm leading-relaxed whitespace-pre-wrap text-gray-800">
                          {message.content}
                        </p>
                        <div className="flex items-center justify-between mt-3 text-xs text-gray-500">
                          <span>{formatTime(message.timestamp)}</span>
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleCopyMessage(message.content)}
                              className="h-6 w-6 p-0 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100"
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0 rounded-full text-gray-400 hover:text-green-600 hover:bg-green-50"
                            >
                              <ThumbsUp className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0 rounded-full text-gray-400 hover:text-red-600 hover:bg-red-50"
                            >
                              <ThumbsDown className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            ))}

            {/* Typing Indicator */}
            {isTyping && (
              <div className="flex gap-3 justify-start">
                <div className="flex-shrink-0">
                  <div className="w-7 h-7 bg-blue-500 rounded-full flex items-center justify-center">
                    <Bot className="h-4 w-4 text-white" />
                  </div>
                </div>
                <Card className="bg-gray-50">
                  <CardContent className="p-2">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-6 border-t border-gray-200 bg-white flex-shrink-0">
        {/* Model Selector */}
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2">
            <Bot className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">AI模型</span>
          </div>
          <div className="max-w-xs">
            <ModelSelector
              selectedModel={selectedModel}
              onModelChange={setSelectedModel}
              disabled={!transcript}
            />
          </div>
        </div>

        {/* Input Area */}
        <div className="flex gap-3">
          <div className="flex-1">
            <Textarea
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder={
                transcript
                  ? t('ask_about_video')
                  : t('upload_video_first')
              }
              disabled={!transcript}
              className="min-h-[80px] max-h-40 resize-none border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-xl px-4 py-3 text-sm shadow-sm"
              rows={3}
            />
          </div>
          <Button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || !transcript}
            className="self-end h-12 w-12 rounded-xl bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 disabled:bg-gray-300 transition-all duration-200 shadow-sm"
            size="sm"
          >
            <Send className="h-5 w-5" />
          </Button>
        </div>

        {!transcript && (
          <div className="mt-3 p-3 bg-amber-50 border border-amber-200 rounded-lg">
            <p className="text-xs text-amber-700 flex items-center gap-2">
              <span className="w-2 h-2 bg-amber-400 rounded-full"></span>
              {t('generate_transcript_to_chat')}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
