'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Send,
  Bot,
  User,
  Trash2,
  Copy,
  ThumbsUp,
  ThumbsDown,
  Sparkles,
  Settings
} from 'lucide-react';
import { VideoData, TranscriptData, ChatMessage } from '@/types/video.d';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';
import ModelSelector from './model-selector';

interface VideoChatContentProps {
  messages: ChatMessage[];
  onSendMessage: (message: string, model?: string) => void;
  transcript: TranscriptData | null;
  video: VideoData | null;
}

export default function VideoChatContent({ messages, onSendMessage, transcript, video }: VideoChatContentProps) {
  const t = useTranslations('video');
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [selectedModel, setSelectedModel] = useState('openai/gpt-4o-mini');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = () => {
    if (inputMessage.trim()) {
      onSendMessage(inputMessage.trim(), selectedModel);
      setInputMessage('');
      setIsTyping(true);

      // AI回复后停止typing状态
      setTimeout(() => {
        setIsTyping(false);
      }, 3000);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleCopyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
    toast.success(t('message_copied'));
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const suggestedQuestions = [
    t('what_is_main_topic'),
    t('summarize_video'),
    t('key_points'),
    t('explain_concept'),
  ];

  return (
    <div className="h-full bg-white flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <Bot className="h-5 w-5 text-white" />
              </div>
              {t('ai_assistant')}
            </h3>
            <p className="text-sm text-gray-600 mt-1 ml-11">{t('ai_description')}</p>
          </div>
          <Button
            variant="outline"
            size="sm"
            disabled={messages.length === 0}
            className="hover:bg-red-50 hover:border-red-200"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>

        {/* Status */}
        <div className="mt-3 flex items-center gap-2 ml-11">
          <Badge
            variant={video ? 'default' : 'secondary'}
            className={video ? 'bg-green-100 text-green-800 border-green-200' : ''}
          >
            {video ? t('video_loaded') : t('no_video')}
          </Badge>
          <Badge
            variant={transcript ? 'default' : 'secondary'}
            className={transcript ? 'bg-blue-100 text-blue-800 border-blue-200' : ''}
          >
            {transcript ? t('transcript_ready') : t('no_transcript')}
          </Badge>
        </div>

        {/* Model Selector */}
        <div className="mt-4 ml-11">
          <div className="flex items-center gap-2 mb-2">
            <Settings className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">AI模型选择</span>
          </div>
          <div className="max-w-xs">
            <ModelSelector
              selectedModel={selectedModel}
              onModelChange={setSelectedModel}
              disabled={false}
            />
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 min-h-0">
        {messages.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <Sparkles className="h-8 w-8 text-white" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">{t('start_conversation')}</h4>
            <p className="text-gray-600 mb-6 max-w-sm mx-auto leading-relaxed">{t('ai_help_description')}</p>

            {/* Suggested Questions */}
            <div className="space-y-3">
              <p className="text-sm font-semibold text-gray-700 uppercase tracking-wide">{t('suggested_questions')}</p>
              <div className="grid gap-2 max-w-md mx-auto">
                {suggestedQuestions.map((question, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => setInputMessage(question)}
                    className="text-left justify-start p-3 h-auto hover:bg-blue-50 hover:border-blue-200 transition-all duration-200"
                    disabled={!transcript}
                  >
                    <span className="text-sm leading-relaxed">{question}</span>
                  </Button>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex gap-3 ${
                  message.sender === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                {message.sender === 'ai' && (
                  <div className="flex-shrink-0">
                    <div className="w-7 h-7 bg-blue-500 rounded-full flex items-center justify-center">
                      <Bot className="h-4 w-4 text-white" />
                    </div>
                  </div>
                )}
                
                <div className={`max-w-[85%] ${message.sender === 'user' ? 'order-1' : ''}`}>
                  <Card className={`shadow-sm border-0 ${
                    message.sender === 'user'
                      ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
                      : 'bg-white border border-gray-200'
                  }`}>
                    <CardContent className="p-3">
                      <p className="text-sm leading-relaxed whitespace-pre-wrap">
                        {message.content}
                      </p>
                      <div className={`flex items-center justify-between mt-2 text-xs ${
                        message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'
                      }`}>
                        <span>{formatTime(message.timestamp)}</span>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopyMessage(message.content)}
                            className={`h-6 w-6 p-0 rounded-full transition-colors ${
                              message.sender === 'user'
                                ? 'text-blue-100 hover:text-white hover:bg-blue-600'
                                : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
                            }`}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                          {message.sender === 'ai' && (
                            <>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 rounded-full text-gray-400 hover:text-green-600 hover:bg-green-50 transition-colors"
                              >
                                <ThumbsUp className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 rounded-full text-gray-400 hover:text-red-600 hover:bg-red-50 transition-colors"
                              >
                                <ThumbsDown className="h-3 w-3" />
                              </Button>
                            </>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {message.sender === 'user' && (
                  <div className="flex-shrink-0">
                    <div className="w-7 h-7 bg-gray-500 rounded-full flex items-center justify-center">
                      <User className="h-4 w-4 text-white" />
                    </div>
                  </div>
                )}
              </div>
            ))}

            {/* Typing Indicator */}
            {isTyping && (
              <div className="flex gap-3 justify-start">
                <div className="flex-shrink-0">
                  <div className="w-7 h-7 bg-blue-500 rounded-full flex items-center justify-center">
                    <Bot className="h-4 w-4 text-white" />
                  </div>
                </div>
                <Card className="bg-gray-50">
                  <CardContent className="p-2">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200 bg-white flex-shrink-0">
        <div className="flex gap-3">
          <div className="flex-1">
            <Textarea
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder={
                transcript
                  ? t('ask_about_video')
                  : t('upload_video_first')
              }
              disabled={!transcript}
              className="min-h-[60px] max-h-32 resize-none border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-lg px-3 py-2 text-sm"
              rows={2}
            />
          </div>
          <Button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || !transcript}
            className="self-end h-10 w-10 rounded-lg bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 transition-colors"
            size="sm"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>

        {!transcript && (
          <div className="mt-2 p-2 bg-amber-50 border border-amber-200 rounded-lg">
            <p className="text-xs text-amber-700 flex items-center gap-2">
              <span className="w-2 h-2 bg-amber-400 rounded-full"></span>
              {t('generate_transcript_to_chat')}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
