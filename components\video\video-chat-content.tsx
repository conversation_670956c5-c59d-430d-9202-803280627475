'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import {
  Send,
  Bot,
  Trash2,
  Co<PERSON>,
  ThumbsUp,
  ThumbsDown,
  Sparkles,
  MessageSquare,
  Lightbulb,
  HelpCircle,
  FileText
} from 'lucide-react';
import { VideoData, TranscriptData, ChatMessage } from '@/types/video.d';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';
import ModelSelector from './model-selector';

interface VideoChatContentProps {
  messages: ChatMessage[];
  onSendMessage: (message: string, model?: string) => void;
  transcript: TranscriptData | null;
  video: VideoData | null;
}

export default function VideoChatContent({ messages, onSendMessage, transcript }: VideoChatContentProps) {
  const t = useTranslations('video');
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [selectedModel, setSelectedModel] = useState('openai/gpt-4o-mini');
  const [showModelSelector, setShowModelSelector] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 预设问题
  const presetQuestions = [
    { icon: MessageSquare, text: "这个视频的主要内容是什么？", category: "概述" },
    { icon: Lightbulb, text: "视频中有哪些关键观点？", category: "要点" },
    { icon: HelpCircle, text: "能详细解释一下核心概念吗？", category: "解释" },
    { icon: FileText, text: "请总结视频的结论", category: "总结" },
  ];

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = () => {
    if (inputMessage.trim()) {
      onSendMessage(inputMessage.trim(), selectedModel);
      setInputMessage('');
      setIsTyping(true);

      // AI回复后停止typing状态
      setTimeout(() => {
        setIsTyping(false);
      }, 3000);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleCopyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
    toast.success(t('message_copied'));
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };



  return (
    <div className="h-full bg-gray-50 flex flex-col">
      {/* Minimal Header */}
      <div className="p-3 sm:p-4 border-b border-gray-200 bg-white flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 sm:gap-3">
            <div className="w-7 h-7 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
              <Bot className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
            </div>
            <h3 className="text-base sm:text-lg font-semibold text-gray-900">{t('ai_assistant')}</h3>
          </div>
          <Button
            variant="ghost"
            size="sm"
            disabled={messages.length === 0}
            className="text-gray-400 hover:text-red-500 hover:bg-red-50 h-8 w-8 p-0"
          >
            <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-3 sm:p-4 lg:p-6 space-y-4 sm:space-y-6 min-h-0">
        {messages.length === 0 ? (
          <div className="text-center py-8 sm:py-12">
            <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
              <Sparkles className="h-8 w-8 sm:h-10 sm:w-10 text-white" />
            </div>
            <h4 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2 sm:mb-3">{t('start_conversation')}</h4>
            <p className="text-gray-600 mb-6 sm:mb-8 max-w-sm mx-auto leading-relaxed text-sm sm:text-base px-4">{t('ai_help_description')}</p>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                {message.sender === 'ai' && (
                  <div className="flex-shrink-0 mr-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                      <Bot className="h-4 w-4 text-white" />
                    </div>
                  </div>
                )}

                <div className={`${message.sender === 'user' ? 'max-w-[80%] sm:max-w-[70%] lg:max-w-[60%]' : 'flex-1 max-w-[90%] sm:max-w-[85%] lg:max-w-[80%]'}`}>
                  {message.sender === 'user' ? (
                    /* 简化的用户消息 - 响应式 */
                    <div className="bg-blue-500 text-white rounded-2xl rounded-br-md px-3 sm:px-4 py-2 sm:py-3 shadow-sm">
                      <p className="text-sm leading-relaxed break-words">{message.content}</p>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs text-blue-100">{formatTime(message.timestamp)}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCopyMessage(message.content)}
                          className="h-5 w-5 p-0 text-blue-100 hover:text-white hover:bg-blue-600 rounded-full"
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ) : (
                    /* AI消息 - 响应式卡片 */
                    <Card className="bg-white border border-gray-200 shadow-sm">
                      <CardContent className="p-3 sm:p-4">
                        <p className="text-sm leading-relaxed whitespace-pre-wrap text-gray-800 break-words">
                          {message.content}
                        </p>
                        <div className="flex items-center justify-between mt-3 text-xs text-gray-500">
                          <span className="hidden sm:inline">{formatTime(message.timestamp)}</span>
                          <div className="flex items-center gap-1 ml-auto">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleCopyMessage(message.content)}
                              className="h-6 w-6 p-0 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100"
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0 rounded-full text-gray-400 hover:text-green-600 hover:bg-green-50"
                            >
                              <ThumbsUp className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0 rounded-full text-gray-400 hover:text-red-600 hover:bg-red-50"
                            >
                              <ThumbsDown className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            ))}

            {/* Typing Indicator */}
            {isTyping && (
              <div className="flex gap-3 justify-start">
                <div className="flex-shrink-0">
                  <div className="w-7 h-7 bg-blue-500 rounded-full flex items-center justify-center">
                    <Bot className="h-4 w-4 text-white" />
                  </div>
                </div>
                <Card className="bg-gray-50">
                  <CardContent className="p-2">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Enhanced Input Area */}
      <div className="p-3 sm:p-4 border-t border-gray-200 bg-white flex-shrink-0">
        {/* Preset Questions */}
        {transcript && messages.length === 0 && (
          <div className="mb-3 sm:mb-4">
            <div className="text-xs text-gray-500 mb-2 px-1">💡 快速提问</div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {presetQuestions.map((preset, index) => {
                const Icon = preset.icon;
                return (
                  <button
                    key={index}
                    onClick={() => setInputMessage(preset.text)}
                    className="flex items-center gap-2 p-2 sm:p-3 text-left bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-200 rounded-lg transition-all duration-200 text-xs sm:text-sm group"
                  >
                    <Icon className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500 group-hover:text-blue-500 flex-shrink-0 transition-colors" />
                    <span className="text-gray-700 group-hover:text-gray-900 truncate">{preset.text}</span>
                  </button>
                );
              })}
            </div>
          </div>
        )}

        {/* Debug info - 临时显示，帮助调试 */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mb-2 text-xs text-gray-400 px-1">
            Debug: transcript={!!transcript}, messages={messages.length}
          </div>
        )}

        {/* Input Container */}
        <div className="relative">
          <div className="flex gap-2 sm:gap-3">
            <div className="flex-1 relative">
              {/* Main Input */}
              <Textarea
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder={
                  transcript
                    ? t('ask_about_video')
                    : t('upload_video_first')
                }
                disabled={!transcript}
                className="min-h-[60px] sm:min-h-[70px] max-h-32 resize-none border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-xl px-3 sm:px-4 py-2 sm:py-3 pb-10 sm:pb-12 text-sm shadow-sm w-full"
                rows={2}
              />

              {/* Bottom Controls */}
              <div className="absolute bottom-2 left-2 right-12 sm:right-16 flex items-center justify-between">
                {/* Model Selector - Compact */}
                <div className="flex items-center gap-1 sm:gap-2">
                  <div className="relative">
                    <button
                      onClick={() => setShowModelSelector(!showModelSelector)}
                      className="flex items-center gap-1 px-1.5 sm:px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                      disabled={!transcript}
                    >
                      <Bot className="h-3 w-3" />
                      <span className="max-w-16 sm:max-w-20 truncate text-xs">
                        {selectedModel.split('/')[1]?.replace('-', ' ') || 'GPT-4o Mini'}
                      </span>
                    </button>

                    {/* Model Dropdown */}
                    {showModelSelector && (
                      <div className="absolute bottom-full left-0 mb-2 w-56 sm:w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                        <ModelSelector
                          selectedModel={selectedModel}
                          onModelChange={(model) => {
                            setSelectedModel(model);
                            setShowModelSelector(false);
                          }}
                          disabled={!transcript}
                        />
                      </div>
                    )}
                  </div>
                </div>

                {/* Character Count */}
                <div className="text-xs text-gray-400 hidden sm:block">
                  {inputMessage.length}/1000
                </div>
              </div>
            </div>

            {/* Send Button */}
            <Button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim() || !transcript}
              className="self-end h-10 w-10 sm:h-12 sm:w-12 rounded-xl bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 disabled:bg-gray-300 transition-all duration-200 shadow-sm"
              size="sm"
            >
              <Send className="h-4 w-4 sm:h-5 sm:w-5" />
            </Button>
          </div>
        </div>

        {/* Status Message */}
        {!transcript && (
          <div className="mt-3 p-3 bg-amber-50 border border-amber-200 rounded-lg">
            <p className="text-xs text-amber-700 flex items-center gap-2">
              <span className="w-2 h-2 bg-amber-400 rounded-full"></span>
              {t('generate_transcript_to_chat')}
            </p>
          </div>
        )}
      </div>

      {/* Click outside to close dropdowns */}
      {showModelSelector && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowModelSelector(false)}
        />
      )}
    </div>
  );
}
