# Video Processing Feature

This document describes the video processing feature implementation with AI-powered transcription and chat functionality.

## Overview

The video processing feature allows users to upload video files, generate AI transcripts, and interact with an AI assistant to analyze video content. It provides a comprehensive video analysis platform with real-time chat capabilities.

## Features

### 🎥 Video Management
- **File Upload**: Support for MP4, AVI, MOV, WMV, FLV, WebM formats
- **URL Input**: Direct video URL loading with automatic preview
- **Drag & Drop**: Intuitive file upload interface
- **Video Library**: Manage multiple videos with metadata

### 📝 AI Transcription
- **Automatic Transcription**: AI-powered video-to-text conversion
- **Timestamp Support**: Precise time-based segmentation
- **Multiple Languages**: Support for various languages
- **Editable Transcripts**: Edit and save transcript modifications
- **Export Options**: Download transcripts in various formats

### 🤖 AI Chat Assistant
- **Context-Aware Chat**: AI understands video content
- **Suggested Questions**: Pre-built questions for quick analysis
- **Real-time Responses**: Instant AI feedback
- **Chat History**: Persistent conversation tracking
- **Content Analysis**: Deep video content understanding

### 🎮 Video Player
- **Full Controls**: Play, pause, seek, volume control
- **Speed Control**: Variable playback speeds (0.5x - 2x)
- **Timeline Navigation**: Click transcript segments to jump to specific times
- **Responsive Design**: Works on all screen sizes

## File Structure

```
app/
├── [locale]/(default)/video/
│   └── page.tsx                    # Video processing page
components/
└── video/
    ├── video-page-client.tsx       # Main client component
    ├── video-sidebar.tsx           # Video management sidebar
    ├── video-player.tsx            # Video player with controls
    ├── video-transcript.tsx        # Transcript display and editing
    └── video-chat.tsx              # AI chat interface
types/
└── video.d.ts                     # TypeScript type definitions
i18n/
├── messages/
│   ├── en.json                     # English translations
│   └── zh.json                     # Chinese translations
└── pages/landing/
    ├── en.json                     # English navigation
    └── zh.json                     # Chinese navigation
```

## Component Architecture

### VideoPageClient
Main orchestrator component that manages:
- Video selection state
- Transcript generation
- Chat message handling
- Component communication

### VideoSidebar
Video management interface featuring:
- File upload with drag & drop
- URL input with validation
- Video library with metadata
- Processing status indicators

### VideoPlayer
Advanced video player with:
- HTML5 video controls
- Custom control overlay
- Playback speed adjustment
- Timeline scrubbing
- Fullscreen support

### VideoTranscript
Transcript management system:
- Segmented transcript display
- Search functionality
- Edit mode with save/cancel
- Export capabilities
- Timestamp navigation

### VideoChat
AI-powered chat interface:
- Context-aware conversations
- Suggested question prompts
- Message history
- Typing indicators
- Response rating system

## Usage Examples

### Basic Video Upload
```typescript
// Upload a video file
const handleVideoSelect = (video: VideoData) => {
  setSelectedVideo(video);
  // Automatically triggers preview
};
```

### Generate Transcript
```typescript
// Start transcription process
const startTranscription = async () => {
  setIsProcessing(true);
  // Call transcription API
  const transcript = await generateTranscript(video);
  onTranscriptGenerated(transcript);
};
```

### AI Chat Integration
```typescript
// Send message to AI
const handleSendMessage = (message: string) => {
  const newMessage: ChatMessage = {
    content: message,
    sender: 'user',
    timestamp: new Date(),
  };
  setChatMessages(prev => [...prev, newMessage]);
  // AI response handling
};
```

## Type Definitions

### VideoData
```typescript
interface VideoData {
  id: string;
  name: string;
  url: string;
  file?: File;
  duration?: number;
  size?: number;
  type: 'file' | 'url';
  uploadedAt: Date;
}
```

### TranscriptData
```typescript
interface TranscriptData {
  id: string;
  videoId: string;
  segments: TranscriptSegment[];
  fullText: string;
  language?: string;
  generatedAt: Date;
}
```

### ChatMessage
```typescript
interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  relatedSegment?: string;
}
```

## Internationalization

The feature supports multiple languages with comprehensive translations:

### English (en.json)
- Complete UI text translations
- Error messages and notifications
- Help text and descriptions

### Chinese (zh.json)
- Full Chinese language support
- Localized user interface
- Cultural adaptation

## Navigation Integration

Added to main navigation menu:
- **English**: "Video" with video icon
- **Chinese**: "视频处理" with video icon
- **URL**: `/video` route

## Future Enhancements

### Planned Features
1. **Video Analysis**: Advanced content analysis
2. **Subtitle Generation**: Automatic subtitle creation
3. **Multi-language Support**: Extended language options
4. **Cloud Storage**: Video cloud storage integration
5. **Collaboration**: Multi-user video analysis
6. **API Integration**: External video service integration

### Technical Improvements
1. **Performance**: Video streaming optimization
2. **Caching**: Intelligent transcript caching
3. **Offline Support**: Offline video processing
4. **Mobile**: Enhanced mobile experience
5. **Accessibility**: Screen reader support
6. **Analytics**: Usage analytics and insights

## Getting Started

1. **Navigate to Video Page**: Visit `/video` in your application
2. **Upload Video**: Use sidebar to upload or add video URL
3. **Generate Transcript**: Click "Generate Transcript" button
4. **Start Chatting**: Ask AI questions about video content
5. **Explore Features**: Use all available tools and options

The video processing feature provides a complete solution for video analysis, transcription, and AI-powered content understanding, making it easy for users to extract insights from their video content.
