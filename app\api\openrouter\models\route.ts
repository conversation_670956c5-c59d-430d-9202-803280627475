import { NextRequest, NextResponse } from 'next/server';
import { getOpenRouterApiKey, isOpenRouterConfigured } from '@/lib/openrouter';
export const runtime = "edge";

interface OpenRouterModel {
  id: string;
  name: string;
  created?: number;
  description?: string;
  architecture?: {
    input_modalities?: string[];
    output_modalities?: string[];
    tokenizer?: string;
  };
  top_provider?: {
    is_moderated?: boolean;
  };
  pricing?: {
    prompt?: string;
    completion?: string;
    image?: string;
    request?: string;
  };
  context_length?: number;
  per_request_limits?: Record<string, any>;
  supported_parameters?: string[];
}

export async function GET(request: NextRequest) {
  try {
    console.log('Fetching OpenRouter models');

    // Check API key configuration
    const apiKeyConfigured = isOpenRouterConfigured();
    console.log('OpenRouter API key configured:', apiKeyConfigured);
    
    // Verify that OpenRouter is configured
    if (!apiKeyConfigured) {
      console.error('OpenRouter API key not configured');
      return NextResponse.json(
        { 
          error: 'OpenRouter API key not configured. Please add OPENROUTER_API_KEY to your .env.local file.' 
        },
        { status: 500 }
      );
    }

    const openRouterApiKey = getOpenRouterApiKey();
    
    const response = await fetch('https://openrouter.ai/api/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${openRouterApiKey}`,
        'Content-Type': 'application/json',
      },
    });

    console.log('OpenRouter API models response status:', response.status);
    
    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error from OpenRouter API:', errorData);
      return NextResponse.json(
        { error: 'Error from OpenRouter API', details: errorData },
        { status: response.status }
      );
    }

    const data = await response.json();
    
    // Filter models with image input modality
    const allModels: OpenRouterModel[] = data.data || [];
    const visionModels = allModels.filter((model: OpenRouterModel) => 
      model.architecture?.input_modalities?.includes('image')
    );
    
    console.log(`Found ${visionModels.length} vision models out of ${allModels.length} total models`);
    
    return NextResponse.json({
      allModels,
      visionModels
    });
  } catch (error) {
    console.error('Error fetching OpenRouter models:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: (error as Error).message },
      { status: 500 }
    );
  }
} 