import { NextRequest, NextResponse } from 'next/server';

// OpenRouter API配置
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

// 支持的模型列表
const SUPPORTED_MODELS = {
  'openai/gpt-4o-mini': 'GPT-4o Mini',
  'openai/gpt-4o': 'GPT-4o',
  'openai/gpt-3.5-turbo': 'GPT-3.5 Turbo',
  'anthropic/claude-3-haiku': 'Claude 3 Haiku',
  'anthropic/claude-3-sonnet': 'Claude 3 Sonnet',
  'google/gemini-pro': 'Gemini Pro',
  'meta-llama/llama-3.1-8b-instruct': 'Llama 3.1 8B',
  'mistralai/mistral-7b-instruct': 'Mistral 7B',
};

export async function POST(request: NextRequest) {
  try {
    // 检查API密钥
    if (!OPENROUTER_API_KEY) {
      return NextResponse.json(
        { error: 'OpenRouter API key not configured' },
        { status: 500 }
      );
    }

    const body = await request.json();
    const { prompt, type, model = 'mistralai/mistral-7b-instruct' } = body;

    // 验证输入
    if (!prompt || !type) {
      return NextResponse.json(
        { error: 'Missing required fields: prompt and type' },
        { status: 400 }
      );
    }

    // 验证模型
    if (!SUPPORTED_MODELS[model as keyof typeof SUPPORTED_MODELS]) {
      return NextResponse.json(
        { error: `Unsupported model: ${model}` },
        { status: 400 }
      );
    }

    // 根据摘要类型调整系统提示
    const systemPrompts = {
      brief: '你是一个专业的内容摘要专家。请生成简洁明了的摘要，突出核心观点。',
      detailed: '你是一个专业的内容分析师。请生成详细全面的摘要，包含所有重要信息和深层见解。',
      quotes: '你是一个名言收集专家。请提取最有价值、最具启发性的金句，每个金句都要有深度和影响力。',
      keypoints: '你是一个要点提取专家。请将内容整理成清晰的要点列表，每个要点都简洁有力。',
      twitter: '你是一个社交媒体专家。请创建吸引人的Twitter帖子，包含相关话题标签和emoji。',
      linkedin: '你是一个职场内容专家。请创建专业的LinkedIn文章，适合商务和职业发展主题。',
      thread: '你是一个Twitter主题串专家。请创建连贯的推文串，每条推文都有独立价值又相互关联。',
      blog: '你是一个博客写作专家。请创建结构清晰的博客文章大纲，包含吸引人的标题和逻辑清晰的结构。',
    };

    const systemPrompt = systemPrompts[type as keyof typeof systemPrompts] || systemPrompts.brief;

    // 调用OpenRouter API
    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
        'X-Title': 'Video Summary AI',
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 2000,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('OpenRouter API error:', errorData);
      
      return NextResponse.json(
        { 
          error: 'AI service error',
          details: errorData.error?.message || 'Unknown error'
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    
    // 检查响应格式
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      console.error('Invalid OpenRouter response:', data);
      return NextResponse.json(
        { error: 'Invalid AI response format' },
        { status: 500 }
      );
    }

    const content = data.choices[0].message.content;
    
    // 返回结果
    return NextResponse.json({
      content,
      type,
      model,
      usage: data.usage,
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('Summary API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// 获取支持的模型列表
export async function GET() {
  return NextResponse.json({
    models: SUPPORTED_MODELS,
    defaultModel: 'openai/gpt-4o-mini',
  });
}
