import { NextRequest, NextResponse } from 'next/server';
import { getOpenRouterApi<PERSON>ey, isOpenRouterConfigured } from '@/lib/openrouter';

export const runtime = "edge";

// 推荐的总结模型列表
const RECOMMENDED_MODELS = {
  'openai/gpt-4o-mini': { name: 'GPT-4o Mini', category: 'OpenAI', price: 'Low' },
  'openai/gpt-4o': { name: 'GPT-4o', category: 'OpenAI', price: 'High' },
  'openai/gpt-3.5-turbo': { name: 'GPT-3.5 Turbo', category: 'OpenAI', price: 'Low' },
  'anthropic/claude-3-haiku': { name: 'Claude 3 Haiku', category: 'Anthropic', price: 'Low' },
  'anthropic/claude-3-sonnet': { name: 'Claude 3 Sonnet', category: 'Anthropic', price: 'Medium' },
  'anthropic/claude-3-opus': { name: 'Claude 3 Opus', category: 'Anthropic', price: 'High' },
  'google/gemini-pro': { name: 'Gemini Pro', category: 'Google', price: 'Medium' },
  'meta-llama/llama-3.1-8b-instruct': { name: 'Llama 3.1 8B', category: 'Meta', price: 'Low' },
  'meta-llama/llama-3.1-70b-instruct': { name: 'Llama 3.1 70B', category: 'Meta', price: 'Medium' },
  'mistralai/mistral-7b-instruct': { name: 'Mistral 7B', category: 'Mistral', price: 'Low' },
};

export async function POST(request: NextRequest) {
  try {
    // 检查API密钥配置
    const apiKeyConfigured = isOpenRouterConfigured();
    console.log('OpenRouter API key configured:', apiKeyConfigured);

    if (!apiKeyConfigured) {
      console.error('OpenRouter API key not configured');
      return NextResponse.json(
        { error: 'OpenRouter API key not configured. Please add OPENROUTER_API_KEY to your .env.local file.' },
        { status: 500 }
      );
    }

    const openRouterApiKey = getOpenRouterApiKey();
    const body = await request.json();
    const { prompt, type, model = 'openai/gpt-4o-mini' } = body;

    console.log('Processing summary request:', { type, model, promptLength: prompt?.length });

    // 验证输入
    if (!prompt || !type) {
      return NextResponse.json(
        { error: 'Missing required fields: prompt and type' },
        { status: 400 }
      );
    }

    // 验证模型（可选，因为我们允许用户选择任何模型）
    if (!RECOMMENDED_MODELS[model as keyof typeof RECOMMENDED_MODELS]) {
      console.warn(`Using non-recommended model: ${model}`);
    }

    // 根据摘要类型调整系统提示
    const systemPrompts = {
      brief: '你是一个专业的内容摘要专家。请生成简洁明了的摘要，突出核心观点。',
      detailed: '你是一个专业的内容分析师。请生成详细全面的摘要，包含所有重要信息和深层见解。',
      quotes: '你是一个名言收集专家。请提取最有价值、最具启发性的金句，每个金句都要有深度和影响力。',
      keypoints: '你是一个要点提取专家。请将内容整理成清晰的要点列表，每个要点都简洁有力。',
      twitter: '你是一个社交媒体专家。请创建吸引人的Twitter帖子，包含相关话题标签和emoji。',
      linkedin: '你是一个职场内容专家。请创建专业的LinkedIn文章，适合商务和职业发展主题。',
      thread: '你是一个Twitter主题串专家。请创建连贯的推文串，每条推文都有独立价值又相互关联。',
      blog: '你是一个博客写作专家。请创建结构清晰的博客文章大纲，包含吸引人的标题和逻辑清晰的结构。',
    };

    const systemPrompt = systemPrompts[type as keyof typeof systemPrompts] || systemPrompts.brief;

    // 调用OpenRouter API
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openRouterApiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
        'X-Title': 'Video Summary AI',
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 2000,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('OpenRouter API error:', errorData);
      
      return NextResponse.json(
        { 
          error: 'AI service error',
          details: errorData.error?.message || 'Unknown error'
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    
    // 检查响应格式
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      console.error('Invalid OpenRouter response:', data);
      return NextResponse.json(
        { error: 'Invalid AI response format' },
        { status: 500 }
      );
    }

    const content = data.choices[0].message.content;
    
    // 返回结果
    return NextResponse.json({
      content,
      type,
      model,
      usage: data.usage,
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('Summary API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// 获取推荐的模型列表
export async function GET() {
  return NextResponse.json({
    models: RECOMMENDED_MODELS,
    defaultModel: 'openai/gpt-4o-mini',
  });
}
