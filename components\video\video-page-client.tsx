'use client';

import { useState } from 'react';
import VideoSidebar from './video-sidebar';
import SimpleVideoPlayer from './simple-video-player';
import VideoChat from './video-chat';
import { VideoData, TranscriptData, ChatMessage } from '@/types/video.d';

export default function VideoPageClient() {
  const [selectedVideo, setSelectedVideo] = useState<VideoData | null>(null);
  const [transcript, setTranscript] = useState<TranscriptData | null>(null);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleVideoSelect = (video: VideoData) => {
    setSelectedVideo(video);
    setTranscript(null);
    setChatMessages([]);
  };

  const handleTranscriptGenerated = (transcriptData: TranscriptData) => {
    setTranscript(transcriptData);
  };

  const handleSendMessage = async (message: string) => {
    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      content: message,
      sender: 'user',
      timestamp: new Date(),
    };

    setChatMessages(prev => [...prev, newMessage]);

    // 调用AI API生成回复
    try {
      // 构建上下文，包含视频转录信息
      let contextPrompt = message;
      if (transcript && transcript.fullText) {
        contextPrompt = `基于以下视频转录内容回答问题：

视频转录：
${transcript.fullText}

用户问题：${message}

请基于视频内容提供准确、有帮助的回答。如果问题与视频内容无关，请礼貌地说明并尝试引导用户询问与视频相关的问题。`;
      }

      const response = await fetch('/api/ai/summary', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: contextPrompt,
          type: 'chat',
          model: 'openai/gpt-4o-mini', // 可以后续添加模型选择
        }),
      });

      if (!response.ok) {
        throw new Error('AI回复失败');
      }

      const data = await response.json();

      const aiResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: data.content,
        sender: 'ai',
        timestamp: new Date(),
      };

      setChatMessages(prev => [...prev, aiResponse]);
    } catch (error: any) {
      console.error('AI chat error:', error);

      // 添加错误回复
      const errorReply: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: '抱歉，我现在无法回答您的问题。请稍后再试。',
        sender: 'ai',
        timestamp: new Date(),
      };

      setChatMessages(prev => [...prev, errorReply]);
    }
  };

  return (
    <div className="flex h-[calc(100vh-80px)] overflow-hidden">
      {/* Sidebar - Video Library (Narrower) */}
      <div className="w-64 border-r border-gray-200 flex-shrink-0">
        <VideoSidebar
          onVideoSelect={handleVideoSelect}
          selectedVideo={selectedVideo}
          isProcessing={isProcessing}
        />
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex min-w-0 overflow-hidden">
        {/* Video Area - 2/5 */}
        <div className="w-2/5 flex flex-col border-r border-gray-200 min-w-0 overflow-hidden">
          <SimpleVideoPlayer
            video={selectedVideo}
            onVideoSelect={handleVideoSelect}
            onTranscriptGenerated={handleTranscriptGenerated}
            isProcessing={isProcessing}
            setIsProcessing={setIsProcessing}
            transcript={transcript}
          />
        </div>

        {/* Chat Area - 3/5 */}
        <div className="w-3/5 min-w-0 overflow-hidden">
          <VideoChat
            messages={chatMessages}
            onSendMessage={handleSendMessage}
            transcript={transcript}
            video={selectedVideo}
          />
        </div>
      </div>
    </div>
  );
}
