'use client';

import { useState } from 'react';
import VideoSidebar from './video-sidebar';
import VideoPlayer from './video-player';
import VideoTranscript from './video-transcript';
import VideoChat from './video-chat';
import { VideoData, TranscriptData, ChatMessage } from '@/types/video';

export default function VideoPageClient() {
  const [selectedVideo, setSelectedVideo] = useState<VideoData | null>(null);
  const [transcript, setTranscript] = useState<TranscriptData | null>(null);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleVideoSelect = (video: VideoData) => {
    setSelectedVideo(video);
    setTranscript(null);
    setChatMessages([]);
  };

  const handleTranscriptGenerated = (transcriptData: TranscriptData) => {
    setTranscript(transcriptData);
  };

  const handleSendMessage = (message: string) => {
    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      content: message,
      sender: 'user',
      timestamp: new Date(),
    };
    
    setChatMessages(prev => [...prev, newMessage]);
    
    // TODO: 这里将来会调用AI API
    setTimeout(() => {
      const aiResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: `I understand you're asking about: "${message}". Based on the video content, I can help you analyze and discuss the topics covered.`,
        sender: 'ai',
        timestamp: new Date(),
      };
      setChatMessages(prev => [...prev, aiResponse]);
    }, 1000);
  };

  return (
    <div className="flex h-[calc(100vh-80px)]">
      {/* Sidebar - Video Library */}
      <VideoSidebar
        onVideoSelect={handleVideoSelect}
        selectedVideo={selectedVideo}
        isProcessing={isProcessing}
      />

      {/* Main Content Area */}
      <div className="flex-1 flex">
        {/* Left Content - Video and Transcript */}
        <div className="flex-1 flex flex-col">
          {/* Video Player with Upload */}
          <div className="h-1/2 border-b border-gray-200">
            <VideoPlayer
              video={selectedVideo}
              onVideoSelect={handleVideoSelect}
              onTranscriptGenerated={handleTranscriptGenerated}
              isProcessing={isProcessing}
              setIsProcessing={setIsProcessing}
            />
          </div>

          {/* Transcript */}
          <div className="h-1/2">
            <VideoTranscript
              transcript={transcript}
              video={selectedVideo}
            />
          </div>
        </div>

        {/* Right Content - AI Chat (Larger) */}
        <div className="w-[480px] border-l border-gray-200">
          <VideoChat
            messages={chatMessages}
            onSendMessage={handleSendMessage}
            transcript={transcript}
            video={selectedVideo}
          />
        </div>
      </div>
    </div>
  );
}
