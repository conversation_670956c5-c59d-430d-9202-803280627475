'use client';

import { useState } from 'react';
import VideoSidebar from './video-sidebar';
import SimpleVideoPlayer from './simple-video-player';
import VideoChat from './video-chat';
import { VideoData, TranscriptData, ChatMessage } from '@/types/video';

export default function VideoPageClient() {
  const [selectedVideo, setSelectedVideo] = useState<VideoData | null>(null);
  const [transcript, setTranscript] = useState<TranscriptData | null>(null);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleVideoSelect = (video: VideoData) => {
    setSelectedVideo(video);
    setTranscript(null);
    setChatMessages([]);
  };

  const handleTranscriptGenerated = (transcriptData: TranscriptData) => {
    setTranscript(transcriptData);
  };

  const handleSendMessage = (message: string) => {
    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      content: message,
      sender: 'user',
      timestamp: new Date(),
    };
    
    setChatMessages(prev => [...prev, newMessage]);
    
    // TODO: 这里将来会调用AI API
    setTimeout(() => {
      const aiResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: `I understand you're asking about: "${message}". Based on the video content, I can help you analyze and discuss the topics covered.`,
        sender: 'ai',
        timestamp: new Date(),
      };
      setChatMessages(prev => [...prev, aiResponse]);
    }, 1000);
  };

  return (
    <div className="flex h-[calc(100vh-80px)]">
      {/* Sidebar - Video Library (Narrower) */}
      <div className="w-64 border-r border-gray-200">
        <VideoSidebar
          onVideoSelect={handleVideoSelect}
          selectedVideo={selectedVideo}
          isProcessing={isProcessing}
        />
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex">
        {/* Video Area - 2/5 */}
        <div className="w-2/5 flex flex-col border-r border-gray-200">
          <SimpleVideoPlayer
            video={selectedVideo}
            onVideoSelect={handleVideoSelect}
            onTranscriptGenerated={handleTranscriptGenerated}
            isProcessing={isProcessing}
            setIsProcessing={setIsProcessing}
            transcript={transcript}
          />
        </div>

        {/* Chat Area - 3/5 */}
        <div className="w-3/5">
          <VideoChat
            messages={chatMessages}
            onSendMessage={handleSendMessage}
            transcript={transcript}
            video={selectedVideo}
          />
        </div>
      </div>
    </div>
  );
}
