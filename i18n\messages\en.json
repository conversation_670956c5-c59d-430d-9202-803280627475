{"metadata": {"title": "Ship Any AI SaaS Startups in hours | ShipAny", "description": "ShipAny is a NextJS boilerplate for building AI SaaS startups, Ship Fast with a variety of templates and components.", "keywords": "ShipAny, AI SaaS Boilerplate, NextJS Boilerplate"}, "user": {"sign_in": "Sign In", "sign_out": "Sign Out", "credits": "Credits", "api_keys": "API Keys", "my_orders": "My Orders", "user_center": "User Center", "admin_system": "Admin System"}, "sign_modal": {"sign_in_title": "Sign In", "sign_in_description": "Sign in to your account", "sign_up_title": "Sign Up", "sign_up_description": "Create an account", "email_title": "Email", "email_placeholder": "Input your email here", "password_title": "Password", "password_placeholder": "Input your password here", "forgot_password": "Forgot password?", "or": "Or", "continue": "Continue", "no_account": "Don't have an account?", "email_sign_in": "Sign in with <PERSON><PERSON>", "google_sign_in": "Sign in with Google", "github_sign_in": "Sign in with GitHub", "close_title": "Close", "cancel_title": "Cancel"}, "my_orders": {"title": "My Orders", "description": "orders paid with ShipAny.", "no_orders": "No orders found", "tip": "", "activate_order": "Activate Order", "actived": "Activated", "join_discord": "Join <PERSON>", "read_docs": "Read Docs", "table": {"order_no": "Order No", "email": "Email", "product_name": "Product Name", "amount": "Amount", "paid_at": "<PERSON><PERSON>", "github_username": "GitHub Username", "status": "Status"}}, "my_credits": {"title": "My Credits", "left_tip": "left credits: {left_credits}", "no_credits": "No credits records", "recharge": "Recharge", "table": {"trans_no": "Trans No", "trans_type": "Trans Type", "credits": "Credits", "updated_at": "Updated At", "status": "Status"}}, "api_keys": {"title": "API Keys", "tip": "Please keep your apikey safe to avoid leaks", "no_api_keys": "No API Keys", "create_api_key": "Create API Key", "table": {"name": "Name", "key": "Key", "created_at": "Created At"}, "form": {"name": "Name", "name_placeholder": "API Key Name", "submit": "Submit"}}, "blog": {"title": "Blog", "description": "News, resources, and updates about ShipAny", "read_more_text": "Read More"}, "my_invites": {"title": "My Invites", "description": "View your invite records", "no_invites": "No invite records found", "my_invite_link": "My Invite Link", "edit_invite_link": "Edit Invite <PERSON>", "copy_invite_link": "Copy Invite Link", "invite_code": "Invite Code", "invite_tip": "Invite 1 friend to buy ShipAny, reward $50.", "invite_balance": "In<PERSON>te <PERSON>", "total_invite_count": "Total Invite Count", "total_paid_count": "Total Paid Count", "total_award_amount": "Total Award Amount", "update_invite_code": "Set Invite Code", "update_invite_code_tip": "Input your custom invite code", "update_invite_button": "Save", "no_orders": "You can't invite others before you bought ShipAny", "no_affiliates": "You're not allowed to invite others, please contact us to apply for permission.", "table": {"invite_time": "Invite Time", "invite_user": "Invite User", "status": "Status", "reward_percent": "<PERSON><PERSON> Percent", "reward_amount": "<PERSON><PERSON> Amount", "pending": "Pending", "completed": "Completed"}}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "description": "We'd love to hear what went well or how we can improve the product experience.", "submit": "Submit", "loading": "Submitting...", "contact_tip": "Other ways to contact us", "rating_tip": "How do you feel about <PERSON><PERSON><PERSON>?", "placeholder": "Leave your words here..."}, "voice": {"title": "Voice Transcription", "description": "Convert audio files to text using advanced AI speech recognition", "upload_title": "Upload Audio File", "upload_description": "Upload an audio file to transcribe. Supported formats: MP3, WAV, M4A, OGG, FLAC (max 25MB)", "drop_text": "Drop your audio file here or click to browse", "file_info": "MP3, WAV, M4A, OGG, FLAC up to 25MB", "start_transcription": "Start Transcription", "uploading": "Uploading...", "clear": "Clear", "audio_preview": "Audio Preview", "transcription_status": "Transcription Status", "processing": "Processing...", "transcription_result": "Transcription Result", "copy": "Copy", "download": "Download", "timeline": "Timeline", "timeline_description": "Click on any segment to jump to that time in the audio", "confidence": "Confidence", "how_to_use": "How to Use", "step_1_title": "Upload Audio", "step_1_description": "Drag and drop your audio file or click to browse. Supported formats: MP3, WAV, M4A, OGG, FLAC", "step_2_title": "Start Transcription", "step_2_description": "Click \"Start Transcription\" and wait for the AI to process your audio file", "step_3_title": "Get Results", "step_3_description": "View your transcription with timeline, copy to clipboard, or download as text file", "technical_details": "Technical Details", "supported_formats": "Supported Audio Formats", "features": "Features", "max_file_size": "Maximum file size: 25MB", "auto_language": "Automatic language detection", "timestamp_generation": "Timestamp generation", "confidence_scoring": "Confidence scoring", "realtime_status": "Real-time processing status", "high_accuracy": "High Accuracy", "high_accuracy_desc": "Advanced AI models provide highly accurate speech recognition across multiple languages", "multiple_formats": "Multiple Formats", "multiple_formats_desc": "Support for MP3, WAV, M4A, OGG, and FLAC audio formats up to 25MB", "timeline_view": "Timeline View", "timeline_view_desc": "Get detailed timestamps for each segment with clickable timeline navigation", "easy_export": "Easy Export", "easy_export_desc": "Download transcriptions as text files or copy to clipboard instantly"}}