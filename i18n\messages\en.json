{
  "metadata": {
    "title": "Ship Any AI SaaS Startups in hours | ShipAny",
    "description": "ShipAny is a NextJS boilerplate for building AI SaaS startups, Ship Fast with a variety of templates and components.",
    "keywords": "ShipAny, AI SaaS Boilerplate, NextJS Boilerplate"
  },
  "user": {
    "sign_in": "Sign In",
    "sign_out": "Sign Out",
    "credits": "Credits",
    "api_keys": "API Keys",
    "my_orders": "My Orders",
    "user_center": "User Center",
    "admin_system": "Admin System"
  },
  "sign_modal": {
    "sign_in_title": "Sign In",
    "sign_in_description": "Sign in to your account",
    "sign_up_title": "Sign Up",
    "sign_up_description": "Create an account",
    "email_title": "Email",
    "email_placeholder": "Input your email here",
    "password_title": "Password",
    "password_placeholder": "Input your password here",
    "forgot_password": "Forgot password?",
    "or": "Or",
    "continue": "Continue",
    "no_account": "Don't have an account?",
    "email_sign_in": "Sign in with <PERSON><PERSON>",
    "google_sign_in": "Sign in with Google",
    "github_sign_in": "Sign in with GitHub",
    "close_title": "Close",
    "cancel_title": "Cancel"
  },
  "my_orders": {
    "title": "My Orders",
    "description": "orders paid with ShipAny.",
    "no_orders": "No orders found",
    "tip": "",
    "activate_order": "Activate Order",
    "actived": "Activated",
    "join_discord": "Join Discord",
    "read_docs": "Read Docs",
    "table": {
      "order_no": "Order No",
      "email": "Email",
      "product_name": "Product Name",
      "amount": "Amount",
      "paid_at": "Paid At",
      "github_username": "GitHub Username",
      "status": "Status"
    }
  },
  "my_credits": {
    "title": "My Credits",
    "left_tip": "left credits: {left_credits}",
    "no_credits": "No credits records",
    "recharge": "Recharge",
    "table": {
      "trans_no": "Trans No",
      "trans_type": "Trans Type",
      "credits": "Credits",
      "updated_at": "Updated At",
      "status": "Status"
    }
  },
  "api_keys": {
    "title": "API Keys",
    "tip": "Please keep your apikey safe to avoid leaks",
    "no_api_keys": "No API Keys",
    "create_api_key": "Create API Key",
    "table": {
      "name": "Name",
      "key": "Key",
      "created_at": "Created At"
    },
    "form": {
      "name": "Name",
      "name_placeholder": "API Key Name",
      "submit": "Submit"
    }
  },
  "blog": {
    "title": "Blog",
    "description": "News, resources, and updates about ShipAny",
    "read_more_text": "Read More"
  },
  "my_invites": {
    "title": "My Invites",
    "description": "View your invite records",
    "no_invites": "No invite records found",
    "my_invite_link": "My Invite Link",
    "edit_invite_link": "Edit Invite Link",
    "copy_invite_link": "Copy Invite Link",
    "invite_code": "Invite Code",
    "invite_tip": "Invite 1 friend to buy ShipAny, reward $50.",
    "invite_balance": "Invite Reward Balance",
    "total_invite_count": "Total Invite Count",
    "total_paid_count": "Total Paid Count",
    "total_award_amount": "Total Award Amount",
    "update_invite_code": "Set Invite Code",
    "update_invite_code_tip": "Input your custom invite code",
    "update_invite_button": "Save",
    "no_orders": "You can't invite others before you bought ShipAny",
    "no_affiliates": "You're not allowed to invite others, please contact us to apply for permission.",
    "table": {
      "invite_time": "Invite Time",
      "invite_user": "Invite User",
      "status": "Status",
      "reward_percent": "Reward Percent",
      "reward_amount": "Reward Amount",
      "pending": "Pending",
      "completed": "Completed"
    }
  },
  "feedback": {
    "title": "Feedback",
    "description": "We'd love to hear what went well or how we can improve the product experience.",
    "submit": "Submit",
    "loading": "Submitting...",
    "contact_tip": "Other ways to contact us",
    "rating_tip": "How do you feel about ShipAny?",
    "placeholder": "Leave your words here..."
  },
  "voice": {
    "title": "Voice Transcription",
    "description": "Convert audio files to text using advanced AI speech recognition",
    "upload_title": "Audio Input",
    "upload_description": "Upload an audio/video file or provide a URL to transcribe. Supported formats: MP3, WAV, M4A, OGG, FLAC, MP4 (max 25MB)",
    "upload_file": "Upload File",
    "from_url": "From URL",
    "drop_text": "Drop your audio/video file here or click to browse",
    "file_info": "MP3, WAV, M4A, OGG, FLAC, MP4 up to 25MB",
    "url_placeholder": "Enter audio/video file URL (e.g., https://example.com/audio.mp3)",
    "url_description": "Enter a direct link to an audio/video file. Preview will load automatically. Supported formats: MP3, WAV, M4A, OGG, FLAC, MP4",
    "load_url": "Load URL",
    "refresh_preview": "Refresh Preview",
    "url_loaded": "URL loaded successfully",
    "invalid_url": "Please enter a valid audio/video URL",
    "start_transcription": "Start Transcription",
    "uploading": "Uploading...",
    "clear": "Clear",
    "audio_preview": "Audio Preview",
    "video_preview": "Video Preview",
    "media_type": "Media Type",
    "video": "Video",
    "audio": "Audio",
    "file_name": "File Name",
    "file_size": "File Size",
    "source_url": "Source URL"
  },
  "video": {
    "title": "AI Video Processing",
    "subtitle": "Upload videos and generate transcripts with AI assistance",
    "description": "Process videos with AI-powered transcription and analysis",
    "powered_by_ai": "Powered by AI",
    "sidebar_title": "Video Manager",
    "sidebar_description": "Upload and manage your videos",
    "upload_file": "Upload",
    "from_url": "URL",
    "drop_video_here": "Drop video here or click to browse",
    "supported_formats": "MP4, AVI, MOV, WMV, FLV, WebM",
    "video_url_placeholder": "Enter video URL...",
    "add_video": "Add Video",
    "url_description": "Direct links to video files or streaming URLs",
    "recent_videos": "Recent Videos",
    "no_videos_yet": "No videos yet",
    "upload_first_video": "Upload your first video to get started",
    "local_file": "Local",
    "url": "URL",
    "processing_video": "Processing video...",
    "no_video_selected": "No Video Selected",
    "select_video_to_start": "Select a video from the sidebar to start",
    "generate_transcript": "Generate Transcript",
    "processing": "Processing...",
    "starting_transcription": "Starting transcription...",
    "transcription_completed": "Transcription completed!",
    "transcription_failed": "Transcription failed",
    "transcript": "Transcript",
    "transcript_description": "AI-generated video transcript",
    "no_transcript_yet": "No Transcript Available",
    "generate_transcript_instruction": "Click 'Generate Transcript' to extract text from your video",
    "auto_detected": "Auto-detected",
    "segments": "segments",
    "hide_timestamps": "Hide Times",
    "show_timestamps": "Show Times",
    "edit": "Edit",
    "copy": "Copy",
    "download": "Download",
    "save": "Save",
    "cancel": "Cancel",
    "search_transcript": "Search transcript...",
    "edit_transcript_placeholder": "Edit your transcript here...",
    "full_text": "Full Text",
    "confidence": "Confidence",
    "characters": "characters",
    "words": "words",
    "search_results": "results found",
    "transcript_copied": "Transcript copied to clipboard",
    "transcript_downloaded": "Transcript downloaded",
    "transcript_saved": "Transcript saved",
    "ai_assistant": "AI Assistant",
    "ai_description": "Ask questions about your video content",
    "video_loaded": "Video Ready",
    "no_video": "No Video",
    "transcript_ready": "Transcript Ready",
    "no_transcript": "No Transcript",
    "start_conversation": "Start a Conversation",
    "ai_help_description": "I can help you analyze and understand your video content",
    "suggested_questions": "Suggested Questions",
    "what_is_main_topic": "What is the main topic of this video?",
    "summarize_video": "Can you summarize this video?",
    "key_points": "What are the key points discussed?",
    "explain_concept": "Can you explain the main concepts?",
    "message_copied": "Message copied",
    "chat_cleared": "Chat cleared",
    "ask_about_video": "Ask about the video content...",
    "upload_video_first": "Upload a video and generate transcript first",
    "generate_transcript_to_chat": "Generate a transcript to start chatting with AI"
    "transcription_status": "Transcription Status",
    "processing": "Processing...",
    "transcription_result": "Transcription Result",
    "copy": "Copy",
    "download": "Download",
    "timeline": "Timeline",
    "timeline_description": "Click on any segment to jump to that time in the audio",
    "confidence": "Confidence",
    "duration": "Duration",
    "task": "Task",
    "language": "Language",
    "select_task": "Select task",
    "transcribe": "Transcribe",
    "translate": "Translate",
    "auto_detect": "Auto Detect (Recommended)",
    "transcribe_description": "Convert speech to text in the original language",
    "translate_description": "Convert speech to English text (translation)",
    "how_to_use": "How to Use",
    "step_1_title": "Upload Audio",
    "step_1_description": "Drag and drop your audio file or click to browse. Supported formats: MP3, WAV, M4A, OGG, FLAC",
    "step_1_description_updated": "Upload an audio/video file or enter a URL. Supported formats: MP3, WAV, M4A, OGG, FLAC, MP4",
    "step_2_title": "Start Transcription",
    "step_2_description": "Click \"Start Transcription\" and wait for the AI to process your audio file",
    "step_3_title": "Get Results",
    "step_3_description": "View your transcription with timeline, copy to clipboard, or download as text file",
    "technical_details": "Technical Details",
    "supported_formats": "Supported Audio Formats",
    "features": "Features",
    "max_file_size": "Maximum file size: 25MB",
    "url_input_support": "Direct URL input support",
    "auto_language": "Automatic language detection",
    "timestamp_generation": "Timestamp generation",
    "confidence_scoring": "Confidence scoring",
    "realtime_status": "Real-time processing status",
    "high_accuracy": "High Accuracy",
    "high_accuracy_desc": "Advanced AI models provide highly accurate speech recognition across multiple languages",
    "multiple_formats": "Multiple Formats",
    "multiple_formats_desc": "Support for MP3, WAV, M4A, OGG, FLAC audio and MP4 video formats up to 25MB",
    "timeline_view": "Timeline View",
    "timeline_view_desc": "Get detailed timestamps for each segment with clickable timeline navigation",
    "easy_export": "Easy Export",
    "easy_export_desc": "Download transcriptions as text files or copy to clipboard instantly"
  }
}
