import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

export async function POST(request: NextRequest) {
  try {
    console.log('Replicate API key configured:', !!process.env.REPLICATE_API_TOKEN);

    const formData = await request.formData();
    const video = formData.get('video') as File | string;
    const task = formData.get('task') as string || 'transcribe';
    const language = formData.get('language') as string || 'None';
    const batch_size = parseInt(formData.get('batch_size') as string) || 64;
    const return_timestamps = formData.get('return_timestamps') === 'true';

    console.log('Processing video transcription request:', {
      hasVideo: !!video,
      task,
      batch_size,
      return_timestamps,
      language
    });

    if (!video) {
      return NextResponse.json(
        { error: 'No video file provided' },
        { status: 400 }
      );
    }

    let videoData: string;

    if (typeof video === 'string') {
      // URL input
      videoData = video;
    } else {
      // File upload - convert to base64
      const bytes = await video.arrayBuffer();
      const buffer = Buffer.from(bytes);
      videoData = `data:${video.type};base64,${buffer.toString('base64')}`;
    }

    // Use Replicate's Whisper model for video transcription
    const output = await replicate.run(
      "openai/whisper:4d50797290df275329f202e48c76360b3f22b08d28c196cbc54600319435f8d2",
      {
        input: {
          task,
          audio: videoData,
          batch_size,
          return_timestamps,
          ...(language && language !== 'None' && { language })
        }
      }
    );

    console.log('Replicate response received');

    // Transform the output to match our expected format
    const transformedOutput = {
      segments: [],
      text: '',
      language: language === 'None' ? 'auto-detected' : language
    };

    if (output && typeof output === 'object') {
      if ('segments' in output && Array.isArray(output.segments)) {
        transformedOutput.segments = output.segments.map((segment: any, index: number) => ({
          id: (index + 1).toString(),
          start: segment.start || 0,
          end: segment.end || 0,
          text: segment.text || '',
          confidence: segment.confidence || 0.9
        }));
      }
      
      if ('text' in output && typeof output.text === 'string') {
        transformedOutput.text = output.text;
      } else if (transformedOutput.segments.length > 0) {
        transformedOutput.text = transformedOutput.segments.map((s: any) => s.text).join(' ');
      }
    }

    return NextResponse.json(transformedOutput);

  } catch (error: any) {
    console.error('Error in video transcription:', error);
    
    if (error.message?.includes('input validation failed')) {
      return NextResponse.json(
        { 
          error: 'Input validation failed', 
          details: error.message,
          suggestion: 'Please check your video format and language settings'
        },
        { status: 422 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to process video transcription',
        details: error.message || 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}
